# RuoYi-Go 核心结构说明

## 数据权限过滤机制

RuoYi框架提供了灵活的数据权限过滤机制，能够根据用户角色和部门关系动态构建SQL查询条件，限制用户只能查看特定范围内的数据。

### 数据权限类型

系统支持以下几种数据权限类型：

1. **全部数据权限（1）**：可以查看所有数据。
2. **自定数据权限（2）**：可以查看自定义指定部门的数据。
3. **本部门数据权限（3）**：只能查看本部门的数据。
4. **本部门及以下数据权限（4）**：可以查看本部门及以下部门的数据。
5. **仅本人数据权限（5）**：只能查看本人创建的数据。

### 实现方式

数据权限过滤的实现主要涉及以下几个组件：

#### 1. 数据权限常量定义

位于 `middleware/data_scope.go` 文件中，定义了与数据权限相关的常量：

```go
const (
    // 数据权限类型
    DATA_SCOPE_ALL        = "1" // 全部数据权限
    DATA_SCOPE_CUSTOM     = "2" // 自定数据权限
    DATA_SCOPE_DEPT       = "3" // 本部门数据权限
    DATA_SCOPE_DEPT_AND_CHILD = "4" // 本部门及以下数据权限
    DATA_SCOPE_SELF       = "5" // 仅本人数据权限
    
    // 数据权限参数键名
    DATA_SCOPE = "dataScope" // 数据范围参数名
)
```

#### 2. 数据权限注解

位于 `annotation/data_scope.go` 文件中，定义了与数据权限相关的注解：

```go
// DataPermission 数据权限过滤注解
type DataPermission struct {
    // 部门别名
    DeptAlias string
    // 用户别名
    UserAlias string
    // 权限字符（用于多个角色匹配符合要求的权限）
    Permission string
    // 是否过滤数据权限，默认过滤
    IsFilter bool
}

// MatchingPermission 匹配权限字符
func MatchingPermission(permissions []string, permission string) bool {
    // 如果配置了权限字符串，则进行匹配
    if permission != "" {
        for _, p := range permissions {
            if p == permission {
                return true
            }
        }
        return false
    }
    return true
}
```

#### 3. 数据权限过滤方法

位于 `middleware/data_scope.go` 文件中的 `DataScopeFilter` 方法是核心实现：

```go
// DataScopeFilter 数据权限过滤
func DataScopeFilter(ctx context.Context, user *model.SysUser, deptAlias, userAlias, permission string, baseEntity *model.BaseEntity) {
    // ...
    // 根据角色权限范围构建SQL条件
    // ...
}
```

该方法会根据用户的角色和部门关系，构建相应的SQL条件，并将其存储在 `baseEntity.Params` 中，供后续查询使用。

#### 4. 在服务层中应用数据权限过滤

在各个服务层方法中，通过调用 `DataScopeFilter` 方法来应用数据权限过滤：

```go
// 以角色服务的ListRoles方法为例
func (s *roleServiceImpl) ListRoles(ctx context.Context, role *model.SysRole, pageNum, pageSize int) ([]*model.SysRole, int64, error) {
    // ...
    baseEntity := &model.BaseEntity{}
    // ...
    
    // 获取当前登录用户
    user := getUserFromContext(ctx)
    if user != nil {
        // 调用数据权限过滤
        middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)
        
        // 应用数据权限过滤
        if baseEntity.Params != nil {
            if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
                // 拼接数据权限SQL
                query = query.Joins("LEFT JOIN sys_role_dept rd ON rd.role_id = sys_role.role_id")
                query = query.Joins("LEFT JOIN sys_dept d ON d.dept_id = rd.dept_id")
                query = query.Where(dataScope)
                // 去重
                query = query.Group("sys_role.role_id")
            }
        }
    }
    // ...
}
```

### 数据权限相关表

数据权限过滤机制依赖于以下几个关联表：

1. **sys_role**：角色表，包含 `data_scope` 字段，用于定义角色的数据权限范围。
2. **sys_role_dept**：角色部门关联表，用于自定义数据权限场景下的部门范围定义。
3. **sys_user_role**：用户角色关联表，用于获取用户所属角色。
4. **sys_dept**：部门表，用于构建部门层级关系。

### 数据权限使用示例

在业务层中使用数据权限过滤的典型流程如下：

1. 在查询方法中创建基础实体：`baseEntity := &model.BaseEntity{}`
2. 从上下文中获取当前登录用户：`user := getUserFromContext(ctx)`
3. 调用数据权限过滤方法：`middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)`
4. 将生成的SQL条件应用到查询中
5. 使用关联查询将相关表连接起来
6. 执行查询并返回结果

通过这种方式，系统能够根据用户的角色和部门关系，自动限制其只能查看有权限的数据，实现了灵活而安全的数据访问控制。 