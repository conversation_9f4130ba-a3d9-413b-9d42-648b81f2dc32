package router

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi-go/internal/api/middleware"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(r *gin.Engine) {
	// 注册全局中间件
	r.Use(middleware.Cors())
	r.Use(middleware.RequestLog())
	r.Use(middleware.Recovery())

	// API前缀
	api := r.Group("/api")
	{
		// 无需认证的接口
		registerPublicRoutes(api)

		// 需要认证的接口
		authorized := api.Group("")
		authorized.Use(middleware.JWT())
		{
			registerSystemRoutes(authorized)
			registerMonitorRoutes(authorized)
			registerToolRoutes(authorized)
		}
	}
}

// registerPublicRoutes 注册公开路由
func registerPublicRoutes(api *gin.Engine) {
	// TODO: 实现登录、注册等公开接口
}

// registerSystemRoutes 注册系统管理路由
func registerSystemRoutes(api *gin.RouterGroup) {
	// TODO: 实现用户、角色、菜单等系统管理接口
}

// registerMonitorRoutes 注册监控管理路由
func registerMonitorRoutes(api *gin.RouterGroup) {
	// TODO: 实现服务监控、操作日志等监控接口
}

// registerToolRoutes 注册工具管理路由
func registerToolRoutes(api *gin.RouterGroup) {
	// TODO: 实现代码生成、系统接口等工具接口
}
