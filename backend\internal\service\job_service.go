package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/ruoyi-go/internal/model"
	"github.com/ruoyi-go/internal/utils"
	"gorm.io/gorm"
)

// JobService 定时任务服务接口
type JobService interface {
	// 任务查询
	GetJobById(ctx context.Context, jobId uint64) (*model.SysJob, error)
	ListJobs(ctx context.Context, job *model.SysJob, pageNum, pageSize int) ([]*model.SysJob, int64, error)

	// 任务操作
	CreateJob(ctx context.Context, job *model.SysJob) error
	UpdateJob(ctx context.Context, job *model.SysJob) error
	DeleteJobByIds(ctx context.Context, jobIds []uint64) error

	// 任务调度
	ChangeJobStatus(ctx context.Context, job *model.SysJob) error
	RunJob(ctx context.Context, job *model.SysJob) error
	CheckCronExprValid(cronExpr string) (bool, error)

	// 日志
	ListJobLogs(ctx context.Context, jobLog *model.SysJobLog, pageNum, pageSize int) ([]*model.SysJobLog, int64, error)
	DeleteJobLogByIds(ctx context.Context, logIds []uint64) error
	CleanJobLog(ctx context.Context) error

	// 服务控制
	StartJobService(ctx context.Context) error
	StopJobService(ctx context.Context) error
}

// jobServiceImpl 定时任务服务实现
type jobServiceImpl struct {
	db        *gorm.DB
	scheduler *cron.Cron
	jobMap    map[uint64]cron.EntryID
	cache     *utils.CacheUtils
	running   bool
}

// NewJobService 创建定时任务服务
func NewJobService(db *gorm.DB) JobService {
	return &jobServiceImpl{
		db:        db,
		scheduler: cron.New(cron.WithSeconds()),
		jobMap:    make(map[uint64]cron.EntryID),
		cache:     utils.NewCacheUtils(),
		running:   false,
	}
}

// GetJobById 根据任务ID获取任务
func (s *jobServiceImpl) GetJobById(ctx context.Context, jobId uint64) (*model.SysJob, error) {
	var job model.SysJob
	err := s.db.Where("job_id = ?", jobId).First(&job).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

// ListJobs 查询任务列表
func (s *jobServiceImpl) ListJobs(ctx context.Context, job *model.SysJob, pageNum, pageSize int) ([]*model.SysJob, int64, error) {
	var jobs []*model.SysJob
	var total int64

	// 构建查询条件
	query := s.db.Model(&model.SysJob{})

	if job != nil {
		if job.JobName != "" {
			query = query.Where("job_name like ?", "%"+job.JobName+"%")
		}
		if job.JobGroup != "" {
			query = query.Where("job_group = ?", job.JobGroup)
		}
		if job.Status != "" {
			query = query.Where("status = ?", job.Status)
		}
		if job.InvokeTarget != "" {
			query = query.Where("invoke_target like ?", "%"+job.InvokeTarget+"%")
		}
	}

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	err := query.Find(&jobs).Error
	if err != nil {
		return nil, 0, err
	}

	return jobs, total, nil
}

// CreateJob 创建任务
func (s *jobServiceImpl) CreateJob(ctx context.Context, job *model.SysJob) error {
	// 校验Cron表达式
	valid, err := s.CheckCronExprValid(job.CronExpression)
	if err != nil {
		return err
	}
	if !valid {
		return errors.New("Cron表达式不正确")
	}

	// 设置默认值
	if job.Status == "" {
		job.Status = "0" // 默认启用
	}

	// 设置创建时间
	job.CreateTime = time.Now()

	// 创建任务
	if err := s.db.Create(job).Error; err != nil {
		return err
	}

	// 如果任务已启用且调度器正在运行，则添加到调度器
	if job.Status == "0" && s.running {
		return s.addJobToScheduler(job)
	}

	return nil
}

// UpdateJob 更新任务
func (s *jobServiceImpl) UpdateJob(ctx context.Context, job *model.SysJob) error {
	// 校验Cron表达式
	valid, err := s.CheckCronExprValid(job.CronExpression)
	if err != nil {
		return err
	}
	if !valid {
		return errors.New("Cron表达式不正确")
	}

	// 设置更新时间
	job.UpdateTime = time.Now()

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 查询原任务
		var oldJob model.SysJob
		if err := tx.Where("job_id = ?", job.JobId).First(&oldJob).Error; err != nil {
			return err
		}

		// 更新任务
		if err := tx.Model(&model.SysJob{}).Where("job_id = ?", job.JobId).
			Omit("create_time").Updates(job).Error; err != nil {
			return err
		}

		// 如果调度器正在运行
		if s.running {
			// 如果原任务已启用，从调度器中移除
			if oldJob.Status == "0" {
				s.removeJobFromScheduler(job.JobId)
			}

			// 如果新任务启用，添加到调度器
			if job.Status == "0" {
				return s.addJobToScheduler(job)
			}
		}

		return nil
	})
}

// DeleteJobByIds 批量删除任务
func (s *jobServiceImpl) DeleteJobByIds(ctx context.Context, jobIds []uint64) error {
	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 查询任务列表
		var jobs []*model.SysJob
		if err := tx.Where("job_id IN ?", jobIds).Find(&jobs).Error; err != nil {
			return err
		}

		// 删除任务
		if err := tx.Where("job_id IN ?", jobIds).Delete(&model.SysJob{}).Error; err != nil {
			return err
		}

		// 如果调度器正在运行，从调度器中移除任务
		if s.running {
			for _, job := range jobs {
				if job.Status == "0" {
					s.removeJobFromScheduler(job.JobId)
				}
			}
		}

		return nil
	})
}

// ChangeJobStatus 修改任务状态
func (s *jobServiceImpl) ChangeJobStatus(ctx context.Context, job *model.SysJob) error {
	// 设置更新时间
	job.UpdateTime = time.Now()

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 查询原任务
		var oldJob model.SysJob
		if err := tx.Where("job_id = ?", job.JobId).First(&oldJob).Error; err != nil {
			return err
		}

		// 更新任务状态
		if err := tx.Model(&model.SysJob{}).Where("job_id = ?", job.JobId).
			Update("status", job.Status).
			Update("update_time", job.UpdateTime).
			Update("update_by", job.UpdateBy).Error; err != nil {
			return err
		}

		// 如果调度器正在运行
		if s.running {
			// 如果任务启用，添加到调度器
			if job.Status == "0" {
				// 复用原任务的其他信息
				job.JobName = oldJob.JobName
				job.JobGroup = oldJob.JobGroup
				job.InvokeTarget = oldJob.InvokeTarget
				job.CronExpression = oldJob.CronExpression
				job.MisfirePolicy = oldJob.MisfirePolicy
				job.Concurrent = oldJob.Concurrent
				job.JobParams = oldJob.JobParams

				return s.addJobToScheduler(job)
			} else {
				// 如果任务停用，从调度器中移除
				s.removeJobFromScheduler(job.JobId)
			}
		}

		return nil
	})
}

// RunJob 立即执行任务
func (s *jobServiceImpl) RunJob(ctx context.Context, job *model.SysJob) error {
	// 查询任务
	var dbJob model.SysJob
	if err := s.db.Where("job_id = ?", job.JobId).First(&dbJob).Error; err != nil {
		return err
	}

	// 复制必要的参数
	job.JobName = dbJob.JobName
	job.JobGroup = dbJob.JobGroup
	job.InvokeTarget = dbJob.InvokeTarget
	job.CronExpression = dbJob.CronExpression
	job.MisfirePolicy = dbJob.MisfirePolicy
	job.Concurrent = dbJob.Concurrent
	job.JobParams = dbJob.JobParams

	// 创建日志记录
	jobLog := &model.SysJobLog{
		JobName:      job.JobName,
		JobGroup:     job.JobGroup,
		InvokeTarget: job.InvokeTarget,
		JobParams:    job.JobParams,
		StartTime:    time.Now(),
		Status:       "0", // 默认成功
	}

	// 执行任务
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 设置执行结果
				jobLog.Status = "1" // 失败
				jobLog.ExceptionInfo = fmt.Sprintf("%v", r)
			}

			// 设置结束时间
			jobLog.EndTime = time.Now()

			// 计算执行耗时
			jobLog.CostTime = int(jobLog.EndTime.Sub(jobLog.StartTime).Milliseconds())

			// 保存日志
			s.db.Create(jobLog)
		}()

		// 执行任务
		err := s.invokeJob(job)
		if err != nil {
			// 设置执行结果
			jobLog.Status = "1" // 失败
			jobLog.ExceptionInfo = err.Error()
		}
	}()

	return nil
}

// CheckCronExprValid 校验Cron表达式
func (s *jobServiceImpl) CheckCronExprValid(cronExpr string) (bool, error) {
	_, err := cron.ParseStandard(cronExpr)
	if err != nil {
		return false, nil
	}
	return true, nil
}

// ListJobLogs 查询任务日志列表
func (s *jobServiceImpl) ListJobLogs(ctx context.Context, jobLog *model.SysJobLog, pageNum, pageSize int) ([]*model.SysJobLog, int64, error) {
	var logs []*model.SysJobLog
	var total int64

	// 构建查询条件
	query := s.db.Model(&model.SysJobLog{})

	if jobLog != nil {
		if jobLog.JobName != "" {
			query = query.Where("job_name like ?", "%"+jobLog.JobName+"%")
		}
		if jobLog.JobGroup != "" {
			query = query.Where("job_group = ?", jobLog.JobGroup)
		}
		if jobLog.Status != "" {
			query = query.Where("status = ?", jobLog.Status)
		}
		if jobLog.InvokeTarget != "" {
			query = query.Where("invoke_target like ?", "%"+jobLog.InvokeTarget+"%")
		}
		if !jobLog.StartTime.IsZero() && !jobLog.EndTime.IsZero() {
			query = query.Where("start_time BETWEEN ? AND ?", jobLog.StartTime, jobLog.EndTime)
		}
	}

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 排序
	query = query.Order("job_log_id DESC")

	// 执行查询
	err := query.Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// DeleteJobLogByIds 批量删除任务日志
func (s *jobServiceImpl) DeleteJobLogByIds(ctx context.Context, logIds []uint64) error {
	return s.db.Where("job_log_id IN ?", logIds).Delete(&model.SysJobLog{}).Error
}

// CleanJobLog 清空任务日志
func (s *jobServiceImpl) CleanJobLog(ctx context.Context) error {
	return s.db.Exec("TRUNCATE TABLE sys_job_log").Error
}

// StartJobService 启动任务调度服务
func (s *jobServiceImpl) StartJobService(ctx context.Context) error {
	if s.running {
		return errors.New("任务调度服务已经启动")
	}

	// 查询所有启用的任务
	var jobs []*model.SysJob
	if err := s.db.Where("status = ?", "0").Find(&jobs).Error; err != nil {
		return err
	}

	// 添加到调度器
	for _, job := range jobs {
		if err := s.addJobToScheduler(job); err != nil {
			return err
		}
	}

	// 启动调度器
	s.scheduler.Start()
	s.running = true

	return nil
}

// StopJobService 停止任务调度服务
func (s *jobServiceImpl) StopJobService(ctx context.Context) error {
	if !s.running {
		return errors.New("任务调度服务未启动")
	}

	// 停止调度器
	s.scheduler.Stop()
	s.running = false

	// 清空任务映射
	s.jobMap = make(map[uint64]cron.EntryID)

	return nil
}

// addJobToScheduler 将任务添加到调度器
func (s *jobServiceImpl) addJobToScheduler(job *model.SysJob) error {
	// 获取任务锁，防止并发添加
	lockKey := utils.JOB_LOCK_KEY + fmt.Sprintf("%d", job.JobId)
	acquired, err := s.cache.SetLock(lockKey, 10*time.Second)
	if err != nil {
		return err
	}
	if !acquired {
		return errors.New("获取任务锁失败")
	}
	defer s.cache.ReleaseLock(lockKey)

	// 检查任务是否已在调度器中
	if _, exists := s.jobMap[job.JobId]; exists {
		s.removeJobFromScheduler(job.JobId)
	}

	// 创建定时任务
	entryID, err := s.scheduler.AddFunc(job.CronExpression, func() {
		// 如果是并发执行
		if job.Concurrent == "0" {
			s.runJobInConcurrent(job)
			return
		}

		// 如果不允许并发执行，获取锁
		lockKey := utils.JOB_LOCK_KEY + fmt.Sprintf("%d", job.JobId)
		acquired, err := s.cache.SetLock(lockKey, 60*time.Second)
		if err != nil {
			return
		}
		if !acquired {
			return
		}
		defer s.cache.ReleaseLock(lockKey)

		// 执行任务
		s.runJobInConcurrent(job)
	})

	if err != nil {
		return err
	}

	// 保存任务映射
	s.jobMap[job.JobId] = entryID

	return nil
}

// removeJobFromScheduler 从调度器中移除任务
func (s *jobServiceImpl) removeJobFromScheduler(jobId uint64) {
	entryID, exists := s.jobMap[jobId]
	if exists {
		s.scheduler.Remove(entryID)
		delete(s.jobMap, jobId)
	}
}

// runJobInConcurrent 并发执行任务
func (s *jobServiceImpl) runJobInConcurrent(job *model.SysJob) {
	// 创建日志记录
	jobLog := &model.SysJobLog{
		JobName:      job.JobName,
		JobGroup:     job.JobGroup,
		InvokeTarget: job.InvokeTarget,
		JobParams:    job.JobParams,
		StartTime:    time.Now(),
		Status:       "0", // 默认成功
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 设置执行结果
				jobLog.Status = "1" // 失败
				jobLog.ExceptionInfo = fmt.Sprintf("%v", r)
			}

			// 设置结束时间
			jobLog.EndTime = time.Now()

			// 计算执行耗时
			jobLog.CostTime = int(jobLog.EndTime.Sub(jobLog.StartTime).Milliseconds())

			// 保存日志
			s.db.Create(jobLog)
		}()

		// 执行任务
		err := s.invokeJob(job)
		if err != nil {
			// 设置执行结果
			jobLog.Status = "1" // 失败
			jobLog.ExceptionInfo = err.Error()
		}
	}()
}

// invokeJob 执行任务
func (s *jobServiceImpl) invokeJob(job *model.SysJob) error {
	// 解析任务目标
	targetInfo := utils.ParseInvokeTarget(job.InvokeTarget)
	if targetInfo == nil {
		return errors.New("无法解析任务目标")
	}

	// 查找任务执行器
	executor := GetJobExecutor(targetInfo.Name)
	if executor == nil {
		return fmt.Errorf("未找到任务执行器: %s", targetInfo.Name)
	}

	// 解析任务参数
	var params []interface{}
	if job.JobParams != "" {
		// 尝试解析JSON参数
		var jsonParams []interface{}
		err := json.Unmarshal([]byte(job.JobParams), &jsonParams)
		if err == nil {
			params = jsonParams
		} else {
			// 如果不是JSON数组，则作为单个参数
			params = []interface{}{job.JobParams}
		}
	}

	// 调用方法
	return callMethod(executor, targetInfo.Method, params)
}

// 任务目标信息
type JobTargetInfo struct {
	Name   string // 执行器名称
	Method string // 方法名称
}

// JobExecutor 任务执行器接口
type JobExecutor interface {
	Execute(params ...interface{}) error
}

// 任务执行器映射
var jobExecutors = make(map[string]JobExecutor)

// RegisterJobExecutor 注册任务执行器
func RegisterJobExecutor(name string, executor JobExecutor) {
	jobExecutors[name] = executor
}

// GetJobExecutor 获取任务执行器
func GetJobExecutor(name string) JobExecutor {
	return jobExecutors[name]
}

// callMethod 调用方法
func callMethod(target interface{}, methodName string, params []interface{}) error {
	// 获取目标类型和值
	targetValue := reflect.ValueOf(target)

	// 查找方法
	method := targetValue.MethodByName(methodName)
	if !method.IsValid() {
		return fmt.Errorf("未找到方法: %s", methodName)
	}

	// 准备参数
	var args []reflect.Value
	for _, param := range params {
		args = append(args, reflect.ValueOf(param))
	}

	// 调用方法
	results := method.Call(args)

	// 检查返回值
	if len(results) > 0 {
		// 如果最后一个返回值是error类型
		lastResult := results[len(results)-1]
		if lastResult.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
			if !lastResult.IsNil() {
				return lastResult.Interface().(error)
			}
		}
	}

	return nil
}
