package service

import (
	"context"
	"errors"
	"sort"
	"strings"
	"time"

	"github.com/ruoyi-go/internal/model"
	"gorm.io/gorm"
)

// 菜单类型常量
const (
	TYPE_DIR    = "M" // 目录
	TYPE_MENU   = "C" // 菜单
	TYPE_BUTTON = "F" // 按钮
)

// 是否外链常量
const (
	NO_FRAME  = "0" // 非外链
	YES_FRAME = "1" // 是外链
)

// 路由常量
const (
	PATH_PARENT_VIEW = "Layout" // 父级菜单视图
)

// 显示状态常量
const (
	SHOW = "0" // 显示
	HIDE = "1" // 隐藏
)

// MenuService 菜单服务接口
type MenuService interface {
	// 查询菜单
	GetMenuById(ctx context.Context, menuId uint64) (*model.SysMenu, error)
	ListMenus(ctx context.Context, menu *model.SysMenu) ([]*model.SysMenu, error)

	// 菜单操作
	CreateMenu(ctx context.Context, menu *model.SysMenu) error
	UpdateMenu(ctx context.Context, menu *model.SysMenu) error
	DeleteMenuById(ctx context.Context, menuId uint64) error

	// 角色菜单
	GetMenusByRoleId(ctx context.Context, roleId uint64) ([]*model.SysMenu, error)
	GetMenuPermsByRoleId(ctx context.Context, roleId uint64) ([]string, error)
	GetMenuPermsByUserId(ctx context.Context, userId uint64) ([]string, error)

	// 用户菜单
	GetMenusByUserId(ctx context.Context, userId uint64) ([]*model.SysMenu, error)
	GetMenuTreeByUserId(ctx context.Context, userId uint64) ([]*model.SysMenu, error)
	BuildMenuTree(menus []*model.SysMenu) []*model.SysMenu
	BuildMenuTreeSelect(menus []*model.SysMenu) []*model.TreeSelect

	// 路由
	BuildRouters(menus []*model.SysMenu) []*model.RouterVo

	// 校验
	CheckMenuNameUnique(ctx context.Context, menuId uint64, menuName string, parentId uint64) (bool, error)
	HasChildByMenuId(ctx context.Context, menuId uint64) (bool, error)
}

// menuServiceImpl 菜单服务实现
type menuServiceImpl struct {
	db *gorm.DB
}

// NewMenuService 创建菜单服务
func NewMenuService(db *gorm.DB) MenuService {
	return &menuServiceImpl{
		db: db,
	}
}

// GetMenuById 根据菜单ID获取菜单信息
func (s *menuServiceImpl) GetMenuById(ctx context.Context, menuId uint64) (*model.SysMenu, error) {
	var menu model.SysMenu
	err := s.db.Where("menu_id = ?", menuId).First(&menu).Error
	if err != nil {
		return nil, err
	}
	return &menu, nil
}

// ListMenus 查询菜单列表，支持数据权限过滤
func (s *menuServiceImpl) ListMenus(ctx context.Context, menu *model.SysMenu) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu

	// 初始化查询条件
	query := s.db.Model(&model.SysMenu{})

	// 添加查询条件
	if menu != nil {
		if menu.MenuName != "" {
			query = query.Where("menu_name like ?", "%"+menu.MenuName+"%")
		}
		if menu.Status != "" {
			query = query.Where("status = ?", menu.Status)
		}
		if menu.MenuId > 0 {
			query = query.Where("menu_id = ?", menu.MenuId)
		}
		if menu.ParentId > 0 {
			query = query.Where("parent_id = ?", menu.ParentId)
		}
	}

	// 排序
	query = query.Order("parent_id, order_num")

	// 查询菜单列表
	err := query.Find(&menus).Error
	if err != nil {
		return nil, err
	}

	return menus, nil
}

// CreateMenu 创建菜单
func (s *menuServiceImpl) CreateMenu(ctx context.Context, menu *model.SysMenu) error {
	// 校验菜单名称是否唯一
	isUnique, err := s.CheckMenuNameUnique(ctx, 0, menu.MenuName, menu.ParentId)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("新增菜单'" + menu.MenuName + "'失败，菜单名称已存在")
	}

	// 设置路由地址
	if menu.ParentId == 0 && TYPE_DIR == menu.MenuType && NO_FRAME == menu.IsFrame && !strings.HasPrefix(menu.Path, "/") {
		menu.Path = "/" + menu.Path
	}

	// 设置创建时间
	menu.CreateTime = time.Now()

	// 创建菜单
	return s.db.Create(menu).Error
}

// UpdateMenu 修改菜单
func (s *menuServiceImpl) UpdateMenu(ctx context.Context, menu *model.SysMenu) error {
	// 校验菜单名称是否唯一
	isUnique, err := s.CheckMenuNameUnique(ctx, menu.MenuId, menu.MenuName, menu.ParentId)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("修改菜单'" + menu.MenuName + "'失败，菜单名称已存在")
	}

	// 设置路由地址
	if menu.ParentId == 0 && TYPE_DIR == menu.MenuType && NO_FRAME == menu.IsFrame && !strings.HasPrefix(menu.Path, "/") {
		menu.Path = "/" + menu.Path
	}

	// 设置更新时间
	menu.UpdateTime = time.Now()

	// 更新菜单信息
	return s.db.Model(&model.SysMenu{}).Where("menu_id = ?", menu.MenuId).
		Omit("create_time").Updates(menu).Error
}

// DeleteMenuById 删除菜单
func (s *menuServiceImpl) DeleteMenuById(ctx context.Context, menuId uint64) error {
	// 校验菜单是否存在子菜单
	hasChild, err := s.HasChildByMenuId(ctx, menuId)
	if err != nil {
		return err
	}
	if hasChild {
		return errors.New("存在子菜单，不允许删除")
	}

	// 删除菜单
	return s.db.Delete(&model.SysMenu{}, "menu_id = ?", menuId).Error
}

// GetMenusByRoleId 根据角色ID查询菜单列表
func (s *menuServiceImpl) GetMenusByRoleId(ctx context.Context, roleId uint64) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu

	// 查询角色菜单
	err := s.db.Model(&model.SysMenu{}).
		Joins("LEFT JOIN sys_role_menu rm ON rm.menu_id = sys_menu.menu_id").
		Where("rm.role_id = ?", roleId).
		Order("sys_menu.parent_id, sys_menu.order_num").
		Find(&menus).Error

	return menus, err
}

// GetMenuPermsByRoleId 根据角色ID查询菜单权限标识
func (s *menuServiceImpl) GetMenuPermsByRoleId(ctx context.Context, roleId uint64) ([]string, error) {
	var perms []string

	// 查询角色菜单权限
	err := s.db.Model(&model.SysMenu{}).
		Select("DISTINCT sys_menu.perms").
		Joins("LEFT JOIN sys_role_menu rm ON rm.menu_id = sys_menu.menu_id").
		Where("rm.role_id = ? AND sys_menu.perms != ''", roleId).
		Pluck("sys_menu.perms", &perms).Error

	return perms, err
}

// GetMenuPermsByUserId 根据用户ID查询菜单权限标识
func (s *menuServiceImpl) GetMenuPermsByUserId(ctx context.Context, userId uint64) ([]string, error) {
	var perms []string

	// 超级管理员拥有所有权限
	if userId == 1 {
		err := s.db.Model(&model.SysMenu{}).
			Where("perms != ''").
			Pluck("DISTINCT perms", &perms).Error
		return perms, err
	}

	// 查询用户角色菜单权限
	err := s.db.Model(&model.SysMenu{}).
		Select("DISTINCT sys_menu.perms").
		Joins("LEFT JOIN sys_role_menu rm ON rm.menu_id = sys_menu.menu_id").
		Joins("LEFT JOIN sys_user_role ur ON ur.role_id = rm.role_id").
		Where("ur.user_id = ? AND sys_menu.perms != ''", userId).
		Pluck("sys_menu.perms", &perms).Error

	return perms, err
}

// GetMenusByUserId 根据用户ID查询菜单列表
func (s *menuServiceImpl) GetMenusByUserId(ctx context.Context, userId uint64) ([]*model.SysMenu, error) {
	var menus []*model.SysMenu

	// 超级管理员拥有所有菜单
	if userId == 1 {
		err := s.db.Model(&model.SysMenu{}).
			Where("status = ? AND del_flag = '0'", SHOW).
			Order("parent_id, order_num").
			Find(&menus).Error
		return menus, err
	}

	// 查询用户角色菜单
	err := s.db.Model(&model.SysMenu{}).
		Distinct("sys_menu.*").
		Joins("LEFT JOIN sys_role_menu rm ON rm.menu_id = sys_menu.menu_id").
		Joins("LEFT JOIN sys_user_role ur ON ur.role_id = rm.role_id").
		Where("ur.user_id = ? AND sys_menu.status = ? AND sys_menu.del_flag = '0'", userId, SHOW).
		Order("sys_menu.parent_id, sys_menu.order_num").
		Find(&menus).Error

	return menus, err
}

// GetMenuTreeByUserId 根据用户ID查询菜单树
func (s *menuServiceImpl) GetMenuTreeByUserId(ctx context.Context, userId uint64) ([]*model.SysMenu, error) {
	menus, err := s.GetMenusByUserId(ctx, userId)
	if err != nil {
		return nil, err
	}

	return s.BuildMenuTree(menus), nil
}

// BuildMenuTree 构建菜单树
func (s *menuServiceImpl) BuildMenuTree(menus []*model.SysMenu) []*model.SysMenu {
	// 创建根节点映射
	menuMap := make(map[uint64]*model.SysMenu)
	// 将所有菜单存入映射
	for _, menu := range menus {
		menuMap[menu.MenuId] = menu
	}

	// 构建树形结构
	var trees []*model.SysMenu
	for _, menu := range menus {
		// 如果是根节点，加入树列表
		if menu.ParentId == 0 {
			trees = append(trees, menu)
			continue
		}

		// 如果父节点存在，将当前节点加入父节点的子节点列表
		if parent, ok := menuMap[menu.ParentId]; ok {
			if parent.Children == nil {
				parent.Children = make([]*model.SysMenu, 0)
			}
			parent.Children = append(parent.Children, menu)
		} else {
			// 如果父节点不存在，作为根节点处理
			trees = append(trees, menu)
		}
	}

	// 对树形结构进行排序
	for _, menu := range menuMap {
		if menu.Children != nil && len(menu.Children) > 0 {
			// 按排序字段排序
			sort.Slice(menu.Children, func(i, j int) bool {
				return menu.Children[i].OrderNum < menu.Children[j].OrderNum
			})
		}
	}

	// 对根节点排序
	sort.Slice(trees, func(i, j int) bool {
		return trees[i].OrderNum < trees[j].OrderNum
	})

	return trees
}

// BuildMenuTreeSelect 构建菜单下拉树结构
func (s *menuServiceImpl) BuildMenuTreeSelect(menus []*model.SysMenu) []*model.TreeSelect {
	// 构建菜单树
	menuTrees := s.BuildMenuTree(menus)

	// 转换为下拉树结构
	return s.buildMenuTreeSelect(menuTrees)
}

// buildMenuTreeSelect 内部方法，构建菜单下拉树结构
func (s *menuServiceImpl) buildMenuTreeSelect(menus []*model.SysMenu) []*model.TreeSelect {
	var trees []*model.TreeSelect
	for _, menu := range menus {
		var tree model.TreeSelect
		tree.Id = menu.MenuId
		tree.Label = menu.MenuName

		// 递归处理子节点
		if menu.Children != nil && len(menu.Children) > 0 {
			tree.Children = s.buildMenuTreeSelect(menu.Children)
		}

		trees = append(trees, &tree)
	}

	return trees
}

// BuildRouters 构建前端路由
func (s *menuServiceImpl) BuildRouters(menus []*model.SysMenu) []*model.RouterVo {
	// 构建路由列表
	var routers []*model.RouterVo
	for _, menu := range menus {
		// 只处理目录和菜单
		if menu.ParentId == 0 && (menu.MenuType == TYPE_DIR || menu.MenuType == TYPE_MENU) {
			router := s.getRouter(menu)

			// 处理子路由
			if menu.Children != nil && len(menu.Children) > 0 {
				router.AlwaysShow = true
				router.Redirect = "noRedirect"
				router.Children = s.buildChildrenRouters(menu.Children)
			}

			routers = append(routers, router)
		}
	}

	return routers
}

// getRouter 获取路由信息
func (s *menuServiceImpl) getRouter(menu *model.SysMenu) *model.RouterVo {
	router := &model.RouterVo{
		Hidden:     menu.Status == HIDE,
		Name:       s.getRouteName(menu),
		Path:       s.getRouterPath(menu),
		Component:  s.getComponent(menu),
		Query:      menu.Query,
		AlwaysShow: false,
		Redirect:   "",
		Meta: &model.MetaVo{
			Title:      menu.MenuName,
			Icon:       menu.Icon,
			NoCache:    menu.IsCache == "0",
			Link:       s.getMenuLink(menu),
			ActiveMenu: menu.ActiveMenu,
		},
	}
	return router
}

// getMenuLink 获取菜单外链地址
func (s *menuServiceImpl) getMenuLink(menu *model.SysMenu) string {
	if menu.IsFrame == YES_FRAME && menu.Path != "" {
		return menu.Path
	}
	return ""
}

// buildChildrenRouters 构建子路由
func (s *menuServiceImpl) buildChildrenRouters(menus []*model.SysMenu) []*model.RouterVo {
	var routers []*model.RouterVo
	for _, menu := range menus {
		// 只处理菜单和目录
		if menu.MenuType == TYPE_DIR || menu.MenuType == TYPE_MENU {
			router := s.getRouter(menu)

			// 处理子路由
			if menu.Children != nil && len(menu.Children) > 0 {
				if menu.MenuType == TYPE_DIR {
					router.AlwaysShow = true
					router.Redirect = "noRedirect"
					router.Children = s.buildChildrenRouters(menu.Children)
				} else {
					// 菜单下的按钮不生成路由
					childMenus := s.getChildMenus(menu.Children)
					if len(childMenus) > 0 {
						router.Children = s.buildChildrenRouters(childMenus)
					}
				}
			}

			routers = append(routers, router)
		}
	}

	return routers
}

// getChildMenus 获取子菜单（排除按钮）
func (s *menuServiceImpl) getChildMenus(menus []*model.SysMenu) []*model.SysMenu {
	var childMenus []*model.SysMenu
	for _, menu := range menus {
		if menu.MenuType != TYPE_BUTTON {
			childMenus = append(childMenus, menu)
		}
	}
	return childMenus
}

// getRouteName 获取路由名称
func (s *menuServiceImpl) getRouteName(menu *model.SysMenu) string {
	if menu.MenuType == TYPE_DIR && menu.IsFrame == NO_FRAME {
		return ""
	}
	return strings.Replace(menu.Path, "/", "", -1)
}

// getRouterPath 获取路由地址
func (s *menuServiceImpl) getRouterPath(menu *model.SysMenu) string {
	// 目录
	if menu.ParentId == 0 && menu.MenuType == TYPE_DIR && menu.IsFrame == NO_FRAME {
		return menu.Path
	} else if menu.ParentId == 0 && menu.MenuType == TYPE_DIR && menu.IsFrame == YES_FRAME {
		// 外链目录
		return "/"
	} else if menu.ParentId == 0 && menu.MenuType == TYPE_MENU {
		// 一级菜单
		return menu.Path
	}

	// 子菜单
	return menu.Path
}

// getComponent 获取组件信息
func (s *menuServiceImpl) getComponent(menu *model.SysMenu) string {
	component := menu.Component
	if component == "" {
		if menu.ParentId == 0 && menu.MenuType == TYPE_DIR {
			// 一级目录
			component = PATH_PARENT_VIEW
		} else if menu.MenuType == TYPE_DIR {
			// 子目录
			component = PATH_PARENT_VIEW
		} else if menu.MenuType == TYPE_MENU && menu.ParentId != 0 && menu.IsFrame == NO_FRAME {
			// 子菜单
			component = PATH_PARENT_VIEW
		}
	}
	return component
}

// CheckMenuNameUnique 校验菜单名称是否唯一
func (s *menuServiceImpl) CheckMenuNameUnique(ctx context.Context, menuId uint64, menuName string, parentId uint64) (bool, error) {
	var menu model.SysMenu
	err := s.db.Where("menu_name = ? AND parent_id = ?", menuName, parentId).First(&menu).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 如果是修改，需要排除自身
	if menuId > 0 && menu.MenuId == menuId {
		return true, nil
	}

	return false, nil
}

// HasChildByMenuId 判断菜单是否有子菜单
func (s *menuServiceImpl) HasChildByMenuId(ctx context.Context, menuId uint64) (bool, error) {
	var count int64
	err := s.db.Model(&model.SysMenu{}).Where("parent_id = ?", menuId).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
