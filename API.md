# 若依系统API接口文档

此文档列出了若依系统后端与前端对接的所有API接口。

## 目录

- [认证相关](#认证相关)
- [用户管理](#用户管理)
- [角色管理](#角色管理)
- [菜单管理](#菜单管理)
- [部门管理](#部门管理)
- [岗位管理](#岗位管理)
- [字典管理](#字典管理)
- [参数管理](#参数管理)
- [通知公告](#通知公告)
- [日志管理](#日志管理)
- [系统监控](#系统监控)
- [代码生成](#代码生成)
- [文件管理](#文件管理)

## 认证相关

### 登录

- **POST** `/login`
  - 描述：用户登录
  - 请求体：
    ```json
    {
      "username": "admin",
      "password": "admin123",
      "code": "验证码",
      "uuid": "验证码标识"
    }
    ```
  - 响应：
    ```json
    {
      "code": 200,
      "msg": "操作成功",
      "token": "eyJhbGciOiJIUzUxMiJ9..."
    }
    ```

### 获取用户信息

- **GET** `/getInfo`
  - 描述：获取当前登录用户信息
  - 响应：
    ```json
    {
      "user": { /* 用户信息 */ },
      "roles": [ /* 角色集合 */ ],
      "permissions": [ /* 权限集合 */ ]
    }
    ```

### 获取路由

- **GET** `/getRouters`
  - 描述：获取用户菜单路由信息
  - 响应：
    ```json
    {
      "code": 200,
      "data": [ /* 路由菜单树 */ ]
    }
    ```

### 获取验证码

- **GET** `/captchaImage`
  - 描述：获取验证码图片
  - 响应：
    ```json
    {
      "uuid": "验证码标识",
      "img": "Base64编码图片"
    }
    ```

### 退出登录

- **POST** `/logout`
  - 描述：退出登录
  - 响应：
    ```json
    {
      "code": 200,
      "msg": "退出成功"
    }
    ```

## 用户管理

### 用户列表

- **GET** `/system/user/list`
  - 描述：获取用户列表
  - 参数：
    - `pageNum`: 当前页数
    - `pageSize`: 每页条数
    - `userName`: 用户名称（可选）
    - `status`: 用户状态（可选）
    - `deptId`: 部门ID（可选）
    - 其他查询条件（可选）

### 获取用户详细

- **GET** `/system/user/{userId}`
  - 描述：根据用户ID获取详细信息

### 新增用户

- **POST** `/system/user`
  - 描述：新增用户信息

### 修改用户

- **PUT** `/system/user`
  - 描述：修改用户信息

### 删除用户

- **DELETE** `/system/user/{userIds}`
  - 描述：删除用户信息

### 重置密码

- **PUT** `/system/user/resetPwd`
  - 描述：重置用户密码

### 用户状态修改

- **PUT** `/system/user/changeStatus`
  - 描述：修改用户状态

### 获取用户个人信息

- **GET** `/system/user/profile`
  - 描述：获取个人信息

### 修改用户个人信息

- **PUT** `/system/user/profile`
  - 描述：修改个人信息

### 修改用户密码

- **PUT** `/system/user/profile/updatePwd`
  - 描述：修改用户密码

### 头像上传

- **POST** `/system/user/profile/avatar`
  - 描述：上传用户头像

## 角色管理

### 角色列表

- **GET** `/system/role/list`
  - 描述：获取角色列表

### 获取角色详细

- **GET** `/system/role/{roleId}`
  - 描述：根据角色ID获取详细信息

### 新增角色

- **POST** `/system/role`
  - 描述：新增角色信息

### 修改角色

- **PUT** `/system/role`
  - 描述：修改角色信息

### 删除角色

- **DELETE** `/system/role/{roleIds}`
  - 描述：删除角色信息

### 角色状态修改

- **PUT** `/system/role/changeStatus`
  - 描述：修改角色状态

### 角色数据权限

- **PUT** `/system/role/dataScope`
  - 描述：修改角色数据权限

### 获取角色选择框列表

- **GET** `/system/role/optionselect`
  - 描述：获取角色选择框列表

## 菜单管理

### 菜单列表

- **GET** `/system/menu/list`
  - 描述：获取菜单列表

### 获取菜单详细

- **GET** `/system/menu/{menuId}`
  - 描述：根据菜单ID获取详细信息

### 新增菜单

- **POST** `/system/menu`
  - 描述：新增菜单信息

### 修改菜单

- **PUT** `/system/menu`
  - 描述：修改菜单信息

### 删除菜单

- **DELETE** `/system/menu/{menuId}`
  - 描述：删除菜单信息

### 获取菜单下拉树列表

- **GET** `/system/menu/treeselect`
  - 描述：获取菜单下拉树列表

### 获取角色对应菜单树

- **GET** `/system/menu/roleMenuTreeselect/{roleId}`
  - 描述：获取角色对应的菜单树

## 部门管理

### 部门列表

- **GET** `/system/dept/list`
  - 描述：获取部门列表

### 获取部门详细

- **GET** `/system/dept/{deptId}`
  - 描述：根据部门ID获取详细信息

### 新增部门

- **POST** `/system/dept`
  - 描述：新增部门信息

### 修改部门

- **PUT** `/system/dept`
  - 描述：修改部门信息

### 删除部门

- **DELETE** `/system/dept/{deptId}`
  - 描述：删除部门信息

### 获取部门下拉树列表

- **GET** `/system/dept/treeselect`
  - 描述：获取部门下拉树列表

### 获取角色对应部门树

- **GET** `/system/dept/roleDeptTreeselect/{roleId}`
  - 描述：获取角色对应的部门树

## 岗位管理

### 岗位列表

- **GET** `/system/post/list`
  - 描述：获取岗位列表

### 获取岗位详细

- **GET** `/system/post/{postId}`
  - 描述：根据岗位ID获取详细信息

### 新增岗位

- **POST** `/system/post`
  - 描述：新增岗位信息

### 修改岗位

- **PUT** `/system/post`
  - 描述：修改岗位信息

### 删除岗位

- **DELETE** `/system/post/{postIds}`
  - 描述：删除岗位信息

### 获取岗位选择框列表

- **GET** `/system/post/optionselect`
  - 描述：获取岗位选择框列表

## 字典管理

### 字典类型列表

- **GET** `/system/dict/type/list`
  - 描述：获取字典类型列表

### 获取字典类型详细

- **GET** `/system/dict/type/{dictId}`
  - 描述：根据字典类型ID获取详细信息

### 新增字典类型

- **POST** `/system/dict/type`
  - 描述：新增字典类型

### 修改字典类型

- **PUT** `/system/dict/type`
  - 描述：修改字典类型

### 删除字典类型

- **DELETE** `/system/dict/type/{dictIds}`
  - 描述：删除字典类型

### 获取字典选择框列表

- **GET** `/system/dict/type/optionselect`
  - 描述：获取字典选择框列表

### 字典数据列表

- **GET** `/system/dict/data/list`
  - 描述：获取字典数据列表

### 获取字典数据详细

- **GET** `/system/dict/data/{dictCode}`
  - 描述：根据字典编码获取详细信息

### 新增字典数据

- **POST** `/system/dict/data`
  - 描述：新增字典数据

### 修改字典数据

- **PUT** `/system/dict/data`
  - 描述：修改字典数据

### 删除字典数据

- **DELETE** `/system/dict/data/{dictCodes}`
  - 描述：删除字典数据

### 根据字典类型查询字典数据

- **GET** `/system/dict/data/type/{dictType}`
  - 描述：根据字典类型获取字典数据

## 参数管理

### 参数列表

- **GET** `/system/config/list`
  - 描述：获取参数列表

### 获取参数详细

- **GET** `/system/config/{configId}`
  - 描述：根据参数ID获取详细信息

### 新增参数

- **POST** `/system/config`
  - 描述：新增参数配置

### 修改参数

- **PUT** `/system/config`
  - 描述：修改参数配置

### 删除参数

- **DELETE** `/system/config/{configIds}`
  - 描述：删除参数配置

### 刷新参数缓存

- **DELETE** `/system/config/refreshCache`
  - 描述：刷新参数缓存

### 获取参数值

- **GET** `/system/config/configKey/{configKey}`
  - 描述：根据参数键名查询参数值

## 通知公告

### 公告列表

- **GET** `/system/notice/list`
  - 描述：获取通知公告列表

### 获取公告详细

- **GET** `/system/notice/{noticeId}`
  - 描述：根据公告ID获取详细信息

### 新增公告

- **POST** `/system/notice`
  - 描述：新增通知公告

### 修改公告

- **PUT** `/system/notice`
  - 描述：修改通知公告

### 删除公告

- **DELETE** `/system/notice/{noticeIds}`
  - 描述：删除通知公告

## 日志管理

### 操作日志列表

- **GET** `/monitor/operlog/list`
  - 描述：获取操作日志列表

### 删除操作日志

- **DELETE** `/monitor/operlog/{operIds}`
  - 描述：删除操作日志

### 清空操作日志

- **DELETE** `/monitor/operlog/clean`
  - 描述：清空操作日志

### 登录日志列表

- **GET** `/monitor/logininfor/list`
  - 描述：获取登录日志列表

### 删除登录日志

- **DELETE** `/monitor/logininfor/{infoIds}`
  - 描述：删除登录日志

### 清空登录日志

- **DELETE** `/monitor/logininfor/clean`
  - 描述：清空登录日志

### 在线用户列表

- **GET** `/monitor/online/list`
  - 描述：获取在线用户列表

### 强退用户

- **DELETE** `/monitor/online/{tokenId}`
  - 描述：强制退出用户

## 系统监控

### 获取服务器信息

- **GET** `/monitor/server`
  - 描述：获取服务器相关信息

### 获取缓存信息

- **GET** `/monitor/cache`
  - 描述：获取缓存相关信息

### 获取缓存名称列表

- **GET** `/monitor/cache/getNames`
  - 描述：获取缓存名称列表

### 获取缓存键名列表

- **GET** `/monitor/cache/getKeys/{cacheName}`
  - 描述：获取缓存键名列表

### 获取缓存内容

- **GET** `/monitor/cache/getValue/{cacheName}/{cacheKey}`
  - 描述：获取缓存内容

### 清理缓存

- **DELETE** `/monitor/cache/clearCacheName/{cacheName}`
  - 描述：清理指定名称缓存

### 清理所有缓存

- **DELETE** `/monitor/cache/clearAll`
  - 描述：清理所有缓存

## 代码生成

### 查询数据库表列表

- **GET** `/tool/gen/list`
  - 描述：查询数据库表列表

### 查询数据表字段列表

- **GET** `/tool/gen/column/{tableId}`
  - 描述：查询数据表字段列表

### 查询表详细信息

- **GET** `/tool/gen/{tableId}`
  - 描述：查询表详细信息

### 预览代码

- **GET** `/tool/gen/preview/{tableId}`
  - 描述：预览生成代码

### 生成代码（自定义路径）

- **GET** `/tool/gen/genCode/{tableName}`
  - 描述：生成代码

### 同步数据库

- **GET** `/tool/gen/synchDb/{tableName}`
  - 描述：同步数据库

### 批量生成代码

- **GET** `/tool/gen/batchGenCode`
  - 描述：批量生成代码

### 导入表结构

- **POST** `/tool/gen/importTable`
  - 描述：导入表结构

## 文件管理

### 上传文件

- **POST** `/common/upload`
  - 描述：上传文件

### 下载文件

- **GET** `/common/download/resource`
  - 描述：下载文件资源

### 下载导入模板

- **GET** `/*/importTemplate`
  - 描述：下载导入模板（各模块通用）

### 导出Excel

- **POST** `/*/export`
  - 描述：导出Excel（各模块通用） 