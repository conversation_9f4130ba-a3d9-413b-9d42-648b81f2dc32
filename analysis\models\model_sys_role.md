# 角色模型分析

## 模型名称

`SysRole`（系统角色）

## 表名

`sys_role`

## 字段列表

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| role_id | bigint | 角色ID | 主键 |
| role_name | varchar(30) | 角色名称 | 非空，唯一 |
| role_key | varchar(100) | 角色权限字符串 | 非空，唯一 |
| role_sort | int | 显示顺序 | 非空 |
| data_scope | char(1) | 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限） | 默认'1' |
| menu_check_strictly | tinyint(1) | 菜单树选择项是否关联显示 | 默认1 |
| dept_check_strictly | tinyint(1) | 部门树选择项是否关联显示 | 默认1 |
| status | char(1) | 角色状态（0正常 1停用） | 默认'0' |
| del_flag | char(1) | 删除标志（0代表存在 2代表删除） | 默认'0' |
| create_by | varchar(64) | 创建者 | 可空 |
| create_time | datetime | 创建时间 | 可空 |
| update_by | varchar(64) | 更新者 | 可空 |
| update_time | datetime | 更新时间 | 可空 |
| remark | varchar(500) | 备注 | 可空 |

## Java实体类结构

```java
public class SysRole extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    @Excel(name = "角色序号", cellType = ColumnType.NUMERIC)
    private Long roleId;

    /** 角色名称 */
    @Excel(name = "角色名称")
    private String roleName;

    /** 角色权限 */
    @Excel(name = "角色权限")
    private String roleKey;

    /** 角色排序 */
    @Excel(name = "角色排序")
    private Integer roleSort;

    /** 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限） */
    @Excel(name = "数据范围", readConverterExp = "1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5=仅本人数据权限")
    private String dataScope;

    /** 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示） */
    private boolean menuCheckStrictly;

    /** 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ） */
    private boolean deptCheckStrictly;

    /** 角色状态（0正常 1停用） */
    @Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 用户是否存在此角色标识 默认不存在 */
    private boolean flag = false;

    /** 菜单组 */
    private Long[] menuIds;

    /** 部门组（数据权限） */
    private Long[] deptIds;
    
    // getter/setter方法...
}
```

## 关联关系

1. **角色-用户**：多对多关系，一个角色可以分配给多个用户，一个用户可以有多个角色
   - 关联表：`sys_user_role`
   - 关联实体：`SysUser`

2. **角色-菜单**：多对多关系，一个角色可以拥有多个菜单权限，一个菜单可以属于多个角色
   - 关联表：`sys_role_menu`
   - 关联实体：`SysMenu`

3. **角色-部门**：多对多关系，用于数据权限控制，一个角色可以管理多个部门的数据
   - 关联表：`sys_role_dept`
   - 关联实体：`SysDept`

## 验证规则

1. `roleName`：角色名称不能为空，长度限制1-30个字符，且必须唯一
2. `roleKey`：角色权限字符串不能为空，长度限制1-100个字符，且必须唯一
3. `roleSort`：显示顺序必须为数字，用于排序
4. `status`：状态只能是'0'（正常）或'1'（停用）

## 特殊处理

1. 删除角色采用逻辑删除，设置`del_flag = '2'`
2. 管理员角色（如admin）不允许删除
3. 角色状态为停用时，该角色下的用户将无法使用该角色的权限
4. 新增角色时，要同时处理角色与菜单、部门的关联关系
5. 更新角色时，要同时更新角色与菜单、部门的关联关系
6. 数据权限（dataScope）决定了该角色可以访问哪些部门的数据

## GORM映射建议

```go
// SysRole 系统角色
type SysRole struct {
    RoleId             uint64    `gorm:"primary_key;column:role_id;comment:角色ID"`
    RoleName           string    `gorm:"size:30;not null;unique;column:role_name;comment:角色名称"`
    RoleKey            string    `gorm:"size:100;not null;unique;column:role_key;comment:角色权限字符串"`
    RoleSort           int       `gorm:"column:role_sort;comment:显示顺序"`
    DataScope          string    `gorm:"size:1;default:1;column:data_scope;comment:数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）"`
    MenuCheckStrictly  bool      `gorm:"default:true;column:menu_check_strictly;comment:菜单树选择项是否关联显示"`
    DeptCheckStrictly  bool      `gorm:"default:true;column:dept_check_strictly;comment:部门树选择项是否关联显示"`
    Status             string    `gorm:"size:1;default:0;column:status;comment:角色状态（0正常 1停用）"`
    DelFlag            string    `gorm:"size:1;default:0;column:del_flag;comment:删除标志（0代表存在 2代表删除）"`
    CreateBy           string    `gorm:"size:64;column:create_by;comment:创建者"`
    CreateTime         time.Time `gorm:"column:create_time;comment:创建时间"`
    UpdateBy           string    `gorm:"size:64;column:update_by;comment:更新者"`
    UpdateTime         time.Time `gorm:"column:update_time;comment:更新时间"`
    Remark             string    `gorm:"size:500;column:remark;comment:备注"`
    
    // 关联字段
    Users              []*SysUser `gorm:"many2many:sys_user_role;foreignKey:RoleId;joinForeignKey:RoleId;References:UserId;JoinReferences:UserId"`
    Menus              []*SysMenu `gorm:"many2many:sys_role_menu;foreignKey:RoleId;joinForeignKey:RoleId;References:MenuId;JoinReferences:MenuId"`
    Depts              []*SysDept `gorm:"many2many:sys_role_dept;foreignKey:RoleId;joinForeignKey:RoleId;References:DeptId;JoinReferences:DeptId"`
}

// TableName 设置表名
func (SysRole) TableName() string {
    return "sys_role"
}
```

## 迁移注意事项

1. **数据类型映射**：
   - Java的`Long`对应Go的`uint64`
   - Java的`String`对应Go的`string`
   - Java的`Integer`对应Go的`int`
   - Java的`boolean`对应Go的`bool`
   - Java的`Date`对应Go的`time.Time`

2. **关联处理**：
   - GORM使用显式的关联定义
   - 使用`many2many`定义多对多关系
   - 多对多关系需要定义连接表和外键

3. **布尔值处理**：
   - Java中的布尔值在数据库中常用tinyint(1)存储
   - GORM可以自动处理布尔值和tinyint的转换

4. **数据权限处理**：
   - 数据权限是RuoYi框架的特色功能
   - 需要在Go中实现类似的数据权限过滤机制

5. **菜单和部门关联显示**：
   - `menuCheckStrictly`和`deptCheckStrictly`用于控制树形结构的选择行为
   - 需要在前端交互和权限控制中保持一致 