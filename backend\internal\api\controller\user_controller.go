package controller

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi-go/internal/api/request"
	"github.com/ruoyi-go/internal/model"
	"github.com/ruoyi-go/internal/service"
	"github.com/ruoyi-go/pkg/response"
	"github.com/ruoyi-go/pkg/utils"
)

// UserController 用户控制器
type UserController struct {
	UserService service.UserService
}

// NewUserController 创建用户控制器
func NewUserController(userService service.UserService) *UserController {
	return &UserController{
		UserService: userService,
	}
}

// List 获取用户列表
func (c *UserController) List(ctx *gin.Context) {
	var req request.UserListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 构建查询条件
	user := &model.SysUser{
		UserName:    req.UserName,
		Status:      req.Status,
		Phonenumber: req.Phonenumber,
		DeptId:      req.DeptId,
	}

	// 调用服务层获取数据
	list, total, err := c.UserService.ListUsers(ctx, user, req.PageNum, req.PageSize)
	if err != nil {
		response.ErrorMsg(ctx, "查询用户列表失败")
		return
	}

	response.Paginate(ctx, list, total)
}

// GetInfo 获取用户详情
func (c *UserController) GetInfo(ctx *gin.Context) {
	// 获取用户ID
	userId, err := strconv.ParseUint(ctx.Param("userId"), 10, 64)
	if err != nil {
		response.ParamError(ctx, "用户ID格式错误")
		return
	}

	// 调用服务层获取用户信息
	user, err := c.UserService.GetUserById(ctx, userId)
	if err != nil {
		response.ErrorMsg(ctx, "获取用户信息失败")
		return
	}

	// 查询用户角色和岗位信息
	roles, _ := c.UserService.GetUserRoles(ctx, userId)
	posts, _ := c.UserService.GetUserPosts(ctx, userId)

	// 转换角色和岗位ID
	roleIds := make([]uint64, 0, len(roles))
	for _, role := range roles {
		roleIds = append(roleIds, role.RoleId)
	}

	postIds := make([]uint64, 0, len(posts))
	for _, post := range posts {
		postIds = append(postIds, post.PostId)
	}

	user.RoleIds = roleIds
	user.PostIds = postIds

	response.Success(ctx, user)
}

// Add 添加用户
func (c *UserController) Add(ctx *gin.Context) {
	var req request.UserAddRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 校验密码复杂度
	if !utils.IsPasswordValid(req.Password) {
		response.ParamError(ctx, "密码不符合复杂度要求")
		return
	}

	// 构建用户对象
	user := &model.SysUser{
		UserName:    req.UserName,
		NickName:    req.NickName,
		Password:    req.Password,
		DeptId:      req.DeptId,
		Email:       req.Email,
		Phonenumber: req.Phonenumber,
		Sex:         req.Sex,
		Status:      req.Status,
		RoleIds:     req.RoleIds,
		PostIds:     req.PostIds,
		Remark:      req.Remark,
		CreateBy:    "admin", // TODO: 从上下文获取当前用户
	}

	// 调用服务层添加用户
	err := c.UserService.CreateUser(ctx, user)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "添加成功")
}

// Update 更新用户
func (c *UserController) Update(ctx *gin.Context) {
	var req request.UserUpdateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 构建用户对象
	user := &model.SysUser{
		UserId:      req.UserId,
		UserName:    req.UserName,
		NickName:    req.NickName,
		DeptId:      req.DeptId,
		Email:       req.Email,
		Phonenumber: req.Phonenumber,
		Sex:         req.Sex,
		Status:      req.Status,
		RoleIds:     req.RoleIds,
		PostIds:     req.PostIds,
		Remark:      req.Remark,
		UpdateBy:    "admin", // TODO: 从上下文获取当前用户
	}

	// 调用服务层更新用户
	err := c.UserService.UpdateUser(ctx, user)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "修改成功")
}

// Delete 删除用户
func (c *UserController) Delete(ctx *gin.Context) {
	// 获取用户ID字符串
	userIdsStr := ctx.Param("userIds")
	if userIdsStr == "" {
		response.ParamError(ctx, "用户ID不能为空")
		return
	}

	// 解析用户ID数组
	userIds := make([]uint64, 0)
	for _, idStr := range strings.Split(userIdsStr, ",") {
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			response.ParamError(ctx, "用户ID格式错误")
			return
		}
		userIds = append(userIds, id)
	}

	// 调用服务层删除用户
	err := c.UserService.DeleteUserByIds(ctx, userIds)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "删除成功")
}

// ResetPwd 重置密码
func (c *UserController) ResetPwd(ctx *gin.Context) {
	var req request.ResetPwdRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 校验密码复杂度
	if !utils.IsPasswordValid(req.Password) {
		response.ParamError(ctx, "密码不符合复杂度要求")
		return
	}

	// 调用服务层重置密码
	err := c.UserService.ResetUserPassword(ctx, req.UserId, req.Password)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "重置成功")
}

// ChangeStatus 修改用户状态
func (c *UserController) ChangeStatus(ctx *gin.Context) {
	var req request.ChangeStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 调用服务层修改状态
	err := c.UserService.UpdateUserStatus(ctx, req.UserId, req.Status)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "状态修改成功")
}

// AuthRole 获取用户授权角色
func (c *UserController) AuthRole(ctx *gin.Context) {
	// 获取用户ID
	userId, err := strconv.ParseUint(ctx.Param("userId"), 10, 64)
	if err != nil {
		response.ParamError(ctx, "用户ID格式错误")
		return
	}

	// 获取用户信息
	user, err := c.UserService.GetUserById(ctx, userId)
	if err != nil {
		response.ErrorMsg(ctx, "获取用户信息失败")
		return
	}

	// 获取所有角色
	// TODO: 从角色服务获取所有角色

	// 获取用户拥有的角色
	userRoles, err := c.UserService.GetUserRoles(ctx, userId)
	if err != nil {
		response.ErrorMsg(ctx, "获取用户角色失败")
		return
	}

	// 构建响应数据
	data := gin.H{
		"user": map[string]interface{}{
			"userId":   user.UserId,
			"userName": user.UserName,
			"nickName": user.NickName,
		},
		"roles": userRoles,
	}

	response.Success(ctx, data)
}

// InsertAuthRole 保存用户授权角色
func (c *UserController) InsertAuthRole(ctx *gin.Context) {
	var req request.AuthRoleRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 解析角色ID数组
	roleIds := make([]uint64, 0)
	for _, idStr := range strings.Split(req.RoleIds, ",") {
		if idStr == "" {
			continue
		}
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			response.ParamError(ctx, "角色ID格式错误")
			return
		}
		roleIds = append(roleIds, id)
	}

	// 调用服务层保存用户角色
	err := c.UserService.AssignUserRoles(ctx, req.UserId, roleIds)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "授权成功")
}

// Profile 获取个人信息
func (c *UserController) Profile(ctx *gin.Context) {
	// TODO: 从上下文获取当前用户ID
	userId := uint64(1) // 临时使用管理员ID

	// 获取用户信息
	user, err := c.UserService.GetUserById(ctx, userId)
	if err != nil {
		response.ErrorMsg(ctx, "获取用户信息失败")
		return
	}

	// 查询用户角色和岗位
	roles, _ := c.UserService.GetUserRoles(ctx, userId)
	posts, _ := c.UserService.GetUserPosts(ctx, userId)

	// 处理角色和岗位名称
	roleGroup := make([]string, 0, len(roles))
	for _, role := range roles {
		roleGroup = append(roleGroup, role.RoleName)
	}

	postGroup := make([]string, 0, len(posts))
	for _, post := range posts {
		postGroup = append(postGroup, post.PostName)
	}

	// 构建响应数据
	data := gin.H{
		"user":      user,
		"roleGroup": strings.Join(roleGroup, ","),
		"postGroup": strings.Join(postGroup, ","),
	}

	response.Success(ctx, data)
}

// UpdateProfile 更新个人信息
func (c *UserController) UpdateProfile(ctx *gin.Context) {
	var req request.UpdateProfileRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// TODO: 从上下文获取当前用户ID
	userId := uint64(1) // 临时使用管理员ID

	// 构建用户对象
	user := &model.SysUser{
		UserId:      userId,
		NickName:    req.NickName,
		Email:       req.Email,
		Phonenumber: req.Phonenumber,
		Sex:         req.Sex,
		UpdateBy:    "admin", // TODO: 从上下文获取当前用户
	}

	// 调用服务层更新用户信息
	err := c.UserService.UpdateUserProfile(ctx, user)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "修改成功")
}

// UpdatePwd 修改密码
func (c *UserController) UpdatePwd(ctx *gin.Context) {
	var req request.UpdatePwdRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ParamError(ctx, "参数错误")
		return
	}

	// 校验密码复杂度
	if !utils.IsPasswordValid(req.NewPassword) {
		response.ParamError(ctx, "新密码不符合复杂度要求")
		return
	}

	// TODO: 从上下文获取当前用户ID
	userId := uint64(1) // 临时使用管理员ID

	// 调用服务层修改密码
	err := c.UserService.UpdateUserPassword(ctx, userId, req.OldPassword, req.NewPassword)
	if err != nil {
		response.ErrorMsg(ctx, err.Error())
		return
	}

	response.SuccessMsg(ctx, "修改成功")
}

// UploadAvatar 上传头像
func (c *UserController) UploadAvatar(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("avatarfile")
	if err != nil {
		response.ParamError(ctx, "请选择要上传的文件")
		return
	}

	// 校验文件格式和大小
	if !isValidImage(file.Filename) {
		response.ParamError(ctx, "文件格式不正确，请上传png/jpg格式图片")
		return
	}

	if file.Size > 5*1024*1024 {
		response.ParamError(ctx, "文件大小不能超过5MB")
		return
	}

	// TODO: 保存文件到指定路径
	// TODO: 从上下文获取当前用户ID
	userId := uint64(1)                           // 临时使用管理员ID
	avatarPath := "/profile/avatar/2023/test.jpg" // 示例路径

	// 调用服务层更新头像
	err = c.UserService.UpdateUserAvatar(ctx, userId, avatarPath)
	if err != nil {
		response.ErrorMsg(ctx, "更新头像失败")
		return
	}

	response.Success(ctx, gin.H{
		"imgUrl": avatarPath,
	})
}

// 检查是否是有效的图片格式
func isValidImage(filename string) bool {
	ext := strings.ToLower(filename[strings.LastIndex(filename, ".")+1:])
	return ext == "jpg" || ext == "jpeg" || ext == "png"
}
