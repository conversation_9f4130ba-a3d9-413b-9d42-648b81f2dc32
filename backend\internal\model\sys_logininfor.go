package model

import (
	"time"
)

// SysLogininfor 系统访问记录表 sys_logininfor
type SysLogininfor struct {
	BaseEntity
	InfoId        uint64    `json:"infoId" gorm:"column:info_id;primaryKey;comment:访问ID"`
	UserName      string    `json:"userName" gorm:"column:login_name;type:varchar(50);comment:登录账号"`
	Ipaddr        string    `json:"ipaddr" gorm:"column:ipaddr;type:varchar(50);comment:登录IP地址"`
	LoginLocation string    `json:"loginLocation" gorm:"column:login_location;type:varchar(255);comment:登录地点"`
	Browser       string    `json:"browser" gorm:"column:browser;type:varchar(50);comment:浏览器类型"`
	Os            string    `json:"os" gorm:"column:os;type:varchar(50);comment:操作系统"`
	Status        string    `json:"status" gorm:"column:status;type:char(1);default:0;comment:登录状态（0成功 1失败）"`
	Msg           string    `json:"msg" gorm:"column:msg;type:varchar(255);comment:提示消息"`
	LoginTime     time.Time `json:"loginTime" gorm:"column:login_time;comment:访问时间"`
}

// TableName 设置表名
func (SysLogininfor) TableName() string {
	return "sys_logininfor"
}
