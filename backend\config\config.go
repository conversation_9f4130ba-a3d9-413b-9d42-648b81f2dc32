package config

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
)

// AppConfig 全局配置
var AppConfig Config

// Config 应用配置结构
type Config struct {
	App      AppSettings      `mapstructure:"app"`
	Server   ServerSettings   `mapstructure:"server"`
	Database DatabaseSettings `mapstructure:"database"`
	Redis    RedisSettings    `mapstructure:"redis"`
	JWT      JWTSettings      `mapstructure:"jwt"`
	Log      LogSettings      `mapstructure:"log"`
}

// AppSettings 应用设置
type AppSettings struct {
	Name           string `mapstructure:"name"`
	Version        string `mapstructure:"version"`
	CopyrightYear  string `mapstructure:"copyright_year"`
	Profile        string `mapstructure:"profile"`
	AddressEnabled bool   `mapstructure:"address_enabled"`
	CaptchaType    string `mapstructure:"captcha_type"`
}

// ServerSettings 服务器设置
type ServerSettings struct {
	Host        string `mapstructure:"host"`
	Port        int    `mapstructure:"port"`
	ContextPath string `mapstructure:"context_path"`
	Mode        string `mapstructure:"mode"`
}

// DatabaseSettings 数据库设置
type DatabaseSettings struct {
	Driver          string `mapstructure:"driver"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Database        string `mapstructure:"database"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	Charset         string `mapstructure:"charset"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
	ShowSql         bool   `mapstructure:"show_sql"`
}

// RedisSettings Redis设置
type RedisSettings struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Database int    `mapstructure:"database"`
	Timeout  int    `mapstructure:"timeout"`
}

// JWTSettings JWT设置
type JWTSettings struct {
	Secret     string        `mapstructure:"secret"`
	ExpireTime time.Duration `mapstructure:"expire_time"`
	Header     string        `mapstructure:"header"`
}

// LogSettings 日志设置
type LogSettings struct {
	Level      string `mapstructure:"level"`
	Filename   string `mapstructure:"filename"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxBackups int    `mapstructure:"max_backups"`
	MaxAge     int    `mapstructure:"max_age"`
	Compress   bool   `mapstructure:"compress"`
}

// Init 初始化配置
func Init() error {
	viper.SetConfigName("application")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")

	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	if err := viper.Unmarshal(&AppConfig); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	return nil
}
