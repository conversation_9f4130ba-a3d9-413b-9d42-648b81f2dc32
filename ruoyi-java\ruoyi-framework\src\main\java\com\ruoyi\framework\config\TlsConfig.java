package com.ruoyi.framework.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.security.Security;

/**
 * TLS协议配置
 * 用于解决SQL Server 2012使用TLS 1.0而Java默认只接受TLS 1.2和TLS 1.3的问题
 */
@Configuration
public class TlsConfig {
    
    private static final Logger log = LoggerFactory.getLogger(TlsConfig.class);
    
    @Value("${system.properties.https.protocols:TLSv1,TLSv1.1,TLSv1.2,TLSv1.3}")
    private String httpsProtocols;
    
    @PostConstruct
    public void init() {
        try {
            // 设置JVM系统属性
            System.setProperty("https.protocols", httpsProtocols);
            System.setProperty("jdk.tls.client.protocols", httpsProtocols);
            
            // 设置安全属性
            Security.setProperty("jdk.tls.disabledAlgorithms", "");
            Security.setProperty("crypto.policy", "unlimited");
            
            log.info("已配置TLS协议版本: {}", httpsProtocols);
        } catch (Exception e) {
            log.error("配置TLS协议版本失败", e);
        }
    }
} 