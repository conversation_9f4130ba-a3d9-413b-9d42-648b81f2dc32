package model

import (
	"time"
)

// SysDictType 字典类型表 sys_dict_type
type SysDictType struct {
	// 字典主键
	DictId uint64 `gorm:"primary_key;column:dict_id" json:"dictId"`
	// 字典名称
	DictName string `gorm:"column:dict_name" json:"dictName"`
	// 字典类型
	DictType string `gorm:"column:dict_type" json:"dictType"`
	// 状态（0正常 1停用）
	Status string `gorm:"column:status" json:"status"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 更新者
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	// 备注
	Remark string `gorm:"column:remark" json:"remark"`
}

// TableName 表名
func (SysDictType) TableName() string {
	return "sys_dict_type"
}
