package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	// 连接数据库
	dsn := "sqlserver://sa:F@2233@localhost:1433?database=wosm"
	db, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 查询sys_user表结构
	var result []struct {
		ColumnName       string `gorm:"column:COLUMN_NAME"`
		DataType         string `gorm:"column:DATA_TYPE"`
		IsNullable       string `gorm:"column:IS_NULLABLE"`
		ColumnDefault    string `gorm:"column:COLUMN_DEFAULT"`
		CharMaxLength    int64  `gorm:"column:CHARACTER_MAXIMUM_LENGTH"`
		NumericPrecision int64  `gorm:"column:NUMERIC_PRECISION"`
	}

	err = db.Raw(`
		SELECT 
			COLUMN_NAME, 
			DATA_TYPE, 
			IS_NULLABLE, 
			COLUMN_DEFAULT,
			CHARACTER_MAXIMUM_LENGTH,
			NUMERIC_PRECISION
		FROM 
			INFORMATION_SCHEMA.COLUMNS 
		WHERE 
			TABLE_NAME = 'sys_user'
		ORDER BY 
			ORDINAL_POSITION
	`).Scan(&result).Error

	if err != nil {
		log.Fatalf("Failed to query table structure: %v", err)
	}

	fmt.Println("sys_user table structure:")
	fmt.Println("--------------------------------------------------")
	fmt.Printf("%-20s %-15s %-10s %-20s\n", "COLUMN_NAME", "DATA_TYPE", "NULLABLE", "DEFAULT")
	fmt.Println("--------------------------------------------------")
	for _, col := range result {
		fmt.Printf("%-20s %-15s %-10s %-20s\n",
			col.ColumnName,
			getFullDataType(col.DataType, col.CharMaxLength, col.NumericPrecision),
			col.IsNullable,
			col.ColumnDefault)
	}
}

func getFullDataType(dataType string, charLength int64, numericPrecision int64) string {
	switch dataType {
	case "varchar", "nvarchar", "char", "nchar":
		if charLength == -1 {
			return fmt.Sprintf("%s(max)", dataType)
		}
		return fmt.Sprintf("%s(%d)", dataType, charLength)
	case "decimal", "numeric":
		return fmt.Sprintf("%s(%d)", dataType, numericPrecision)
	default:
		return dataType
	}
}
