# 角色管理API接口分析

## 模块概述

角色管理模块是系统权限控制的重要组成部分，主要负责系统角色的增删改查、权限分配、数据权限设置等功能。

## API接口列表

### 1. 角色列表查询

- **接口路径**：`/system/role/list`
- **请求方法**：GET
- **接口说明**：分页查询角色列表，支持多条件筛选
- **权限标识**：`system:role:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| pageNum | integer | 是 | 当前页码 |
| pageSize | integer | 是 | 每页记录数 |
| roleName | string | 否 | 角色名称 |
| roleKey | string | 否 | 权限字符 |
| status | string | 否 | 状态（0正常 1停用） |
| beginTime | string | 否 | 开始时间 |
| endTime | string | 否 | 结束时间 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 10,
    "rows": [
      {
        "roleId": 1,
        "roleName": "超级管理员",
        "roleKey": "admin",
        "roleSort": 1,
        "dataScope": "1",
        "menuCheckStrictly": true,
        "deptCheckStrictly": true,
        "status": "0",
        "createTime": "2023-01-01 12:00:00"
      }
    ]
  }
}
```

### 2. 获取角色详情

- **接口路径**：`/system/role/{roleId}`
- **请求方法**：GET
- **接口说明**：根据角色ID查询角色详细信息
- **权限标识**：`system:role:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roleId": 2,
    "roleName": "普通角色",
    "roleKey": "common",
    "roleSort": 2,
    "dataScope": "2",
    "menuCheckStrictly": true,
    "deptCheckStrictly": true,
    "status": "0",
    "menuIds": [1, 2, 3],
    "deptIds": [100, 101]
  }
}
```

### 3. 新增角色

- **接口路径**：`/system/role`
- **请求方法**：POST
- **接口说明**：新增角色信息
- **权限标识**：`system:role:add`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleName | string | 是 | 角色名称 |
| roleKey | string | 是 | 权限字符 |
| roleSort | integer | 是 | 显示顺序 |
| dataScope | string | 否 | 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限） |
| menuCheckStrictly | boolean | 否 | 菜单树选择项是否关联显示 |
| deptCheckStrictly | boolean | 否 | 部门树选择项是否关联显示 |
| status | string | 否 | 状态（0正常 1停用） |
| remark | string | 否 | 备注 |
| menuIds | array | 否 | 菜单ID数组 |
| deptIds | array | 否 | 部门ID数组（仅当dataScope=2时有效） |

- **请求示例**：
```json
{
  "roleName": "测试角色",
  "roleKey": "test",
  "roleSort": 3,
  "dataScope": "2",
  "menuCheckStrictly": true,
  "deptCheckStrictly": true,
  "status": "0",
  "remark": "测试角色",
  "menuIds": [1, 2, 3],
  "deptIds": [100, 101]
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

### 4. 修改角色

- **接口路径**：`/system/role`
- **请求方法**：PUT
- **接口说明**：修改角色信息
- **权限标识**：`system:role:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| roleName | string | 是 | 角色名称 |
| roleKey | string | 是 | 权限字符 |
| roleSort | integer | 是 | 显示顺序 |
| dataScope | string | 否 | 数据范围 |
| menuCheckStrictly | boolean | 否 | 菜单树选择项是否关联显示 |
| deptCheckStrictly | boolean | 否 | 部门树选择项是否关联显示 |
| status | string | 否 | 状态（0正常 1停用） |
| remark | string | 否 | 备注 |
| menuIds | array | 否 | 菜单ID数组 |
| deptIds | array | 否 | 部门ID数组 |

- **请求示例**：
```json
{
  "roleId": 3,
  "roleName": "测试角色修改",
  "roleKey": "test_update",
  "roleSort": 4,
  "dataScope": "2",
  "menuCheckStrictly": true,
  "deptCheckStrictly": true,
  "status": "0",
  "remark": "测试角色已修改",
  "menuIds": [1, 2, 3, 4],
  "deptIds": [100, 101, 102]
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 5. 删除角色

- **接口路径**：`/system/role/{roleIds}`
- **请求方法**：DELETE
- **接口说明**：删除角色信息
- **权限标识**：`system:role:remove`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleIds | string | 是 | 角色ID串，多个用逗号分隔 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

### 6. 更改角色状态

- **接口路径**：`/system/role/changeStatus`
- **请求方法**：PUT
- **接口说明**：修改角色状态
- **权限标识**：`system:role:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| status | string | 是 | 状态（0正常 1停用） |

- **请求示例**：
```json
{
  "roleId": 3,
  "status": "1"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "状态修改成功"
}
```

### 7. 导出角色

- **接口路径**：`/system/role/export`
- **请求方法**：POST
- **接口说明**：导出角色信息
- **权限标识**：`system:role:export`
- **请求参数**：与列表查询参数相同

- **响应**：返回Excel文件流

### 8. 获取角色已分配用户列表

- **接口路径**：`/system/role/authUser/allocatedList`
- **请求方法**：GET
- **接口说明**：获取已分配用户角色列表
- **权限标识**：`system:role:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| pageNum | integer | 是 | 当前页码 |
| pageSize | integer | 是 | 每页记录数 |
| userName | string | 否 | 用户名称 |
| phonenumber | string | 否 | 手机号码 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 2,
    "rows": [
      {
        "userId": 2,
        "userName": "test",
        "nickName": "测试用户",
        "email": "<EMAIL>",
        "phonenumber": "15888888889",
        "status": "0"
      }
    ]
  }
}
```

### 9. 获取角色未分配用户列表

- **接口路径**：`/system/role/authUser/unallocatedList`
- **请求方法**：GET
- **接口说明**：获取未分配用户角色列表
- **权限标识**：`system:role:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| pageNum | integer | 是 | 当前页码 |
| pageSize | integer | 是 | 每页记录数 |
| userName | string | 否 | 用户名称 |
| phonenumber | string | 否 | 手机号码 |

- **响应示例**：与已分配用户列表响应格式相同

### 10. 批量选择用户授权

- **接口路径**：`/system/role/authUser/selectAll`
- **请求方法**：PUT
- **接口说明**：批量选择用户授权
- **权限标识**：`system:role:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| userIds | string | 是 | 用户ID串，多个用逗号分隔 |

- **请求示例**：
```json
{
  "roleId": 3,
  "userIds": "2,3"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "授权成功"
}
```

### 11. 批量取消用户授权

- **接口路径**：`/system/role/authUser/cancelAll`
- **请求方法**：PUT
- **接口说明**：批量取消用户授权
- **权限标识**：`system:role:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |
| userIds | string | 是 | 用户ID串，多个用逗号分隔 |

- **请求示例**：
```json
{
  "roleId": 3,
  "userIds": "2,3"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "取消授权成功"
}
```

## 接口实现注意事项

1. **角色名称和权限字符唯一性**：
   - 新增和修改角色时需要验证角色名称和权限字符的唯一性

2. **特殊角色保护**：
   - 管理员角色不能被删除或停用
   - 不能删除已分配用户的角色

3. **权限分配**：
   - 修改角色菜单权限时，需要先删除原有关联，再建立新的关联
   - 修改角色数据权限时，如果是自定义数据权限，需要处理角色与部门的关联

4. **用户授权**：
   - 批量选择用户授权和取消授权时，需要对每个用户进行单独处理
   - 需要避免重复授权

5. **角色状态**：
   - 角色停用后，拥有该角色的用户将无法使用该角色的权限

## Gin+GORM实现建议

1. **Controller层**：
```go
// RoleController 处理角色相关请求
type RoleController struct {
    RoleService service.IRoleService
    UserService service.IUserService
}

// List 获取角色列表
func (c *RoleController) List(ctx *gin.Context) {
    // 解析查询参数
    var req request.RoleListRequest
    if err := ctx.ShouldBindQuery(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 调用Service层获取数据
    list, total, err := c.RoleService.SelectRoleList(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    // 返回结果
    response.Success(ctx, gin.H{
        "rows": list,
        "total": total,
    })
}

// GetInfo 获取角色详情
func (c *RoleController) GetInfo(ctx *gin.Context) {
    roleId, err := strconv.ParseUint(ctx.Param("roleId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    role, err := c.RoleService.SelectRoleById(ctx, roleId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    response.Success(ctx, role)
}

// Add 添加角色
func (c *RoleController) Add(ctx *gin.Context) {
    var req request.RoleAddRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 检查角色名称和权限字符唯一性
    if c.RoleService.CheckRoleNameUnique(ctx, req.RoleName) {
        response.Error(ctx, http.StatusBadRequest, "角色名称已存在")
        return
    }
    
    if c.RoleService.CheckRoleKeyUnique(ctx, req.RoleKey) {
        response.Error(ctx, http.StatusBadRequest, "权限字符已存在")
        return
    }
    
    // 创建角色
    err := c.RoleService.InsertRole(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "新增角色失败")
        return
    }
    
    response.Success(ctx, nil)
}

// 其他方法...
```

2. **Service层**：
```go
// IRoleService 角色服务接口
type IRoleService interface {
    SelectRoleList(ctx *gin.Context, req request.RoleListRequest) ([]*models.SysRole, int64, error)
    SelectRoleById(ctx *gin.Context, roleId uint64) (*models.SysRole, error)
    InsertRole(ctx *gin.Context, req request.RoleAddRequest) error
    UpdateRole(ctx *gin.Context, req request.RoleUpdateRequest) error
    DeleteRoleByIds(ctx *gin.Context, roleIds []uint64) error
    ChangeStatus(ctx *gin.Context, roleId uint64, status string) error
    CheckRoleNameUnique(ctx *gin.Context, roleName string) bool
    CheckRoleKeyUnique(ctx *gin.Context, roleKey string) bool
    SelectAllocatedUserList(ctx *gin.Context, roleId uint64, query request.UserListRequest) ([]*models.SysUser, int64, error)
    SelectUnallocatedUserList(ctx *gin.Context, roleId uint64, query request.UserListRequest) ([]*models.SysUser, int64, error)
    InsertAuthUsers(ctx *gin.Context, roleId uint64, userIds []uint64) error
    DeleteAuthUsers(ctx *gin.Context, roleId uint64, userIds []uint64) error
    // 其他方法...
}

// RoleService 角色服务实现
type RoleService struct {
    DB *gorm.DB
}

// SelectRoleList 查询角色列表
func (s *RoleService) SelectRoleList(ctx *gin.Context, req request.RoleListRequest) ([]*models.SysRole, int64, error) {
    var roles []*models.SysRole
    var total int64
    
    db := s.DB.Model(&models.SysRole{}).Where("del_flag = ?", "0")
    
    // 添加查询条件
    if req.RoleName != "" {
        db = db.Where("role_name like ?", "%"+req.RoleName+"%")
    }
    
    if req.RoleKey != "" {
        db = db.Where("role_key like ?", "%"+req.RoleKey+"%")
    }
    
    if req.Status != "" {
        db = db.Where("status = ?", req.Status)
    }
    
    // 统计总数
    db.Count(&total)
    
    // 分页查询
    if err := db.Order("role_sort").Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).Find(&roles).Error; err != nil {
        return nil, 0, err
    }
    
    return roles, total, nil
}

// InsertRole 新增角色
func (s *RoleService) InsertRole(ctx *gin.Context, req request.RoleAddRequest) error {
    // 获取当前用户
    currentUser := auth.GetCurrentUser(ctx)
    
    // 创建角色
    role := &models.SysRole{
        RoleName:          req.RoleName,
        RoleKey:           req.RoleKey,
        RoleSort:          req.RoleSort,
        DataScope:         req.DataScope,
        MenuCheckStrictly: req.MenuCheckStrictly,
        DeptCheckStrictly: req.DeptCheckStrictly,
        Status:            req.Status,
        Remark:            req.Remark,
        CreateBy:          currentUser.UserName,
        CreateTime:        time.Now(),
    }
    
    return s.DB.Transaction(func(tx *gorm.DB) error {
        // 插入角色
        if err := tx.Create(role).Error; err != nil {
            return err
        }
        
        // 插入角色菜单关联
        if len(req.MenuIds) > 0 {
            roleMenus := make([]*models.SysRoleMenu, 0, len(req.MenuIds))
            for _, menuId := range req.MenuIds {
                roleMenus = append(roleMenus, &models.SysRoleMenu{
                    RoleId: role.RoleId,
                    MenuId: menuId,
                })
            }
            if err := tx.Create(roleMenus).Error; err != nil {
                return err
            }
        }
        
        // 如果是自定义数据权限，插入角色部门关联
        if req.DataScope == "2" && len(req.DeptIds) > 0 {
            roleDepts := make([]*models.SysRoleDept, 0, len(req.DeptIds))
            for _, deptId := range req.DeptIds {
                roleDepts = append(roleDepts, &models.SysRoleDept{
                    RoleId: role.RoleId,
                    DeptId: deptId,
                })
            }
            if err := tx.Create(roleDepts).Error; err != nil {
                return err
            }
        }
        
        return nil
    })
}

// 其他方法...
```

3. **请求和响应结构**：
```go
// RoleListRequest 角色列表请求
type RoleListRequest struct {
    PageNum    int    `form:"pageNum"`
    PageSize   int    `form:"pageSize"`
    RoleName   string `form:"roleName"`
    RoleKey    string `form:"roleKey"`
    Status     string `form:"status"`
    BeginTime  string `form:"beginTime"`
    EndTime    string `form:"endTime"`
}

// RoleAddRequest 新增角色请求
type RoleAddRequest struct {
    RoleName          string   `json:"roleName" binding:"required"`
    RoleKey           string   `json:"roleKey" binding:"required"`
    RoleSort          int      `json:"roleSort" binding:"required"`
    DataScope         string   `json:"dataScope"`
    MenuCheckStrictly bool     `json:"menuCheckStrictly"`
    DeptCheckStrictly bool     `json:"deptCheckStrictly"`
    Status            string   `json:"status"`
    Remark            string   `json:"remark"`
    MenuIds           []uint64 `json:"menuIds"`
    DeptIds           []uint64 `json:"deptIds"`
}

// 其他请求结构...
```

4. **路由注册**：
```go
// 注册角色管理路由
func RegisterRoleRoutes(r *gin.RouterGroup, roleController *controller.RoleController) {
    roleRouter := r.Group("/system/role")
    {
        roleRouter.GET("/list", middleware.Auth(), middleware.HasPermission("system:role:list"), roleController.List)
        roleRouter.GET("/:roleId", middleware.Auth(), middleware.HasPermission("system:role:query"), roleController.GetInfo)
        roleRouter.POST("", middleware.Auth(), middleware.HasPermission("system:role:add"), roleController.Add)
        roleRouter.PUT("", middleware.Auth(), middleware.HasPermission("system:role:edit"), roleController.Update)
        roleRouter.DELETE("/:roleIds", middleware.Auth(), middleware.HasPermission("system:role:remove"), roleController.Delete)
        roleRouter.PUT("/changeStatus", middleware.Auth(), middleware.HasPermission("system:role:edit"), roleController.ChangeStatus)
        roleRouter.POST("/export", middleware.Auth(), middleware.HasPermission("system:role:export"), roleController.Export)
        
        // 角色用户授权相关
        authUserRouter := roleRouter.Group("/authUser")
        {
            authUserRouter.GET("/allocatedList", middleware.Auth(), middleware.HasPermission("system:role:list"), roleController.AllocatedList)
            authUserRouter.GET("/unallocatedList", middleware.Auth(), middleware.HasPermission("system:role:list"), roleController.UnallocatedList)
            authUserRouter.PUT("/selectAll", middleware.Auth(), middleware.HasPermission("system:role:edit"), roleController.SelectAll)
            authUserRouter.PUT("/cancelAll", middleware.Auth(), middleware.HasPermission("system:role:edit"), roleController.CancelAll)
        }
    }
} 