# 菜单模型分析

## 模型名称

`SysMenu`（系统菜单）

## 表名

`sys_menu`

## 字段列表

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| menu_id | bigint | 菜单ID | 主键 |
| menu_name | varchar(50) | 菜单名称 | 非空 |
| parent_id | bigint | 父菜单ID | 默认0 |
| order_num | int | 显示顺序 | 默认0 |
| path | varchar(200) | 路由地址 | 可空 |
| component | varchar(255) | 组件路径 | 可空 |
| query | varchar(255) | 路由参数 | 可空 |
| is_frame | int | 是否为外链（0是 1否） | 默认1 |
| is_cache | int | 是否缓存（0缓存 1不缓存） | 默认0 |
| menu_type | char(1) | 菜单类型（M目录 C菜单 F按钮） | 非空 |
| visible | char(1) | 菜单状态（0显示 1隐藏） | 默认'0' |
| status | char(1) | 菜单状态（0正常 1停用） | 默认'0' |
| perms | varchar(100) | 权限标识 | 可空 |
| icon | varchar(100) | 菜单图标 | 可空 |
| create_by | varchar(64) | 创建者 | 可空 |
| create_time | datetime | 创建时间 | 可空 |
| update_by | varchar(64) | 更新者 | 可空 |
| update_time | datetime | 更新时间 | 可空 |
| remark | varchar(500) | 备注 | 可空 |

## Java实体类结构

```java
public class SysMenu extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 菜单ID */
    private Long menuId;

    /** 菜单名称 */
    private String menuName;

    /** 父菜单名称 */
    private String parentName;

    /** 父菜单ID */
    private Long parentId;

    /** 显示顺序 */
    private Integer orderNum;

    /** 路由地址 */
    private String path;

    /** 组件路径 */
    private String component;

    /** 路由参数 */
    private String query;

    /** 是否为外链（0是 1否） */
    private String isFrame;

    /** 是否缓存（0缓存 1不缓存） */
    private String isCache;

    /** 类型（M目录 C菜单 F按钮） */
    private String menuType;

    /** 显示状态（0显示 1隐藏） */
    private String visible;
    
    /** 菜单状态（0正常 1停用） */
    private String status;

    /** 权限字符串 */
    private String perms;

    /** 菜单图标 */
    private String icon;

    /** 子菜单 */
    private List<SysMenu> children = new ArrayList<SysMenu>();
    
    // getter/setter方法...
}
```

## 关联关系

1. **菜单-角色**：多对多关系，一个菜单可以属于多个角色，一个角色可以拥有多个菜单
   - 关联表：`sys_role_menu`
   - 关联实体：`SysRole`

2. **菜单-菜单**：自引用关系，一个菜单可以有多个子菜单，一个子菜单只有一个父菜单
   - 关联字段：`parent_id`
   - 关联实体：`SysMenu`（自身）

## 验证规则

1. `menuName`：菜单名称不能为空
2. `menuType`：菜单类型只能是'M'（目录）、'C'（菜单）或'F'（按钮）
3. `orderNum`：显示顺序必须为数字，用于同级菜单排序
4. 当`menuType=M`时，`component`可以为空
5. 当`menuType=C`时，`component`不能为空，且`perms`通常不为空
6. 当`menuType=F`时，`perms`不能为空，且`path`和`component`通常为空

## 特殊处理

1. 菜单结构为树形结构，通过`parent_id`字段确定层级关系
2. 按钮权限（`menuType=F`）不在菜单树中显示，但用于权限控制
3. 顶级菜单的`parent_id`为0
4. 菜单的显示顺序由`order_num`字段决定，同级菜单按此字段升序排列
5. 菜单权限（`perms`）用于后端接口的权限控制
6. 前端路由由`path`和`component`组成，`query`为路由参数
7. 外链菜单（`is_frame=0`）直接链接到外部地址，不使用内部组件

## GORM映射建议

```go
// SysMenu 系统菜单
type SysMenu struct {
    MenuId      uint64    `gorm:"primary_key;column:menu_id;comment:菜单ID"`
    MenuName    string    `gorm:"size:50;not null;column:menu_name;comment:菜单名称"`
    ParentId    uint64    `gorm:"default:0;column:parent_id;comment:父菜单ID"`
    OrderNum    int       `gorm:"default:0;column:order_num;comment:显示顺序"`
    Path        string    `gorm:"size:200;column:path;comment:路由地址"`
    Component   string    `gorm:"size:255;column:component;comment:组件路径"`
    Query       string    `gorm:"size:255;column:query;comment:路由参数"`
    IsFrame     int       `gorm:"default:1;column:is_frame;comment:是否为外链（0是 1否）"`
    IsCache     int       `gorm:"default:0;column:is_cache;comment:是否缓存（0缓存 1不缓存）"`
    MenuType    string    `gorm:"size:1;not null;column:menu_type;comment:菜单类型（M目录 C菜单 F按钮）"`
    Visible     string    `gorm:"size:1;default:0;column:visible;comment:菜单状态（0显示 1隐藏）"`
    Status      string    `gorm:"size:1;default:0;column:status;comment:菜单状态（0正常 1停用）"`
    Perms       string    `gorm:"size:100;column:perms;comment:权限标识"`
    Icon        string    `gorm:"size:100;column:icon;comment:菜单图标"`
    CreateBy    string    `gorm:"size:64;column:create_by;comment:创建者"`
    CreateTime  time.Time `gorm:"column:create_time;comment:创建时间"`
    UpdateBy    string    `gorm:"size:64;column:update_by;comment:更新者"`
    UpdateTime  time.Time `gorm:"column:update_time;comment:更新时间"`
    Remark      string    `gorm:"size:500;column:remark;comment:备注"`
    
    // 关联字段
    Roles       []*SysRole `gorm:"many2many:sys_role_menu;foreignKey:MenuId;joinForeignKey:MenuId;References:RoleId;JoinReferences:RoleId"`
    Children    []*SysMenu `gorm:"-"` // 不映射到数据库，用于构建树形结构
    ParentName  string     `gorm:"-"` // 父菜单名称，不映射到数据库
}

// TableName 设置表名
func (SysMenu) TableName() string {
    return "sys_menu"
}
```

## 迁移注意事项

1. **数据类型映射**：
   - Java的`Long`对应Go的`uint64`
   - Java的`String`对应Go的`string`
   - Java的`Integer`对应Go的`int`
   - Java的`Date`对应Go的`time.Time`

2. **树形结构处理**：
   - Java版本中使用`children`字段存储子菜单
   - Go版本中也使用`Children`字段，但标记为`gorm:"-"`，表示不映射到数据库
   - 需要单独编写方法来构建树形结构

3. **权限处理**：
   - 权限标识(`perms`)是RuoYi框架权限控制的核心
   - 需要在Go中实现类似的权限验证机制
   - 可以考虑使用Casbin等权限管理库

4. **前端路由处理**：
   - 菜单数据需要转换为Vue路由格式
   - 按钮权限需要单独处理，用于前端组件的权限控制

5. **多级菜单处理**：
   - 在查询时需要自动构建树形结构
   - 可以编写辅助方法将扁平菜单列表转为树形结构 