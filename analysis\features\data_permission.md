# 数据权限功能分析

## 功能概述

数据权限是RuoYi框架的核心特色功能之一，它控制用户能够访问的数据范围，是实现多租户、部门隔离等场景的重要手段。数据权限主要基于角色和部门的关系，对数据查询进行过滤，确保用户只能看到和操作自己有权限的数据。

## 原理分析

### 1. 数据权限的级别

RuoYi框架定义了5种数据权限范围：

1. **全部数据权限（1）**：可以查看所有数据
2. **自定义数据权限（2）**：可以查看指定部门的数据
3. **本部门数据权限（3）**：只能查看自己部门的数据
4. **本部门及以下数据权限（4）**：可以查看自己部门及其所有子部门的数据
5. **仅本人数据权限（5）**：只能查看自己创建的数据

### 2. 数据权限的存储

数据权限主要涉及以下表：

- `sys_role`：角色表，包含`data_scope`字段，存储数据权限类型
- `sys_role_dept`：角色部门关联表，当`data_scope=2`（自定义数据权限）时，存储角色可以访问的部门ID

### 3. 实现方式

在Java版本中，数据权限主要通过以下方式实现：

1. **注解方式**：使用`@DataScope`注解标记需要进行数据权限过滤的方法
2. **SQL拦截**：在查询执行前，动态添加数据权限相关的SQL条件
3. **参数注入**：将数据权限条件作为参数注入到SQL中

实现原理是在查询SQL执行前，根据当前用户的角色和数据权限范围，动态构建WHERE条件，过滤数据。

## Go版本实现方案

在Go版本中，我们可以采用以下方案实现数据权限：

### 1. 中间件实现

创建数据权限中间件，在请求处理前获取当前用户的数据权限范围，并将其存储在上下文中。

```go
// DataScopeMiddleware 数据权限中间件
func DataScopeMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 获取当前登录用户
        user := auth.GetCurrentUser(c)
        if user == nil {
            c.Next()
            return
        }

        // 如果是管理员，不需要数据权限过滤
        if user.IsAdmin() {
            c.Next()
            return
        }

        // 获取用户角色的数据权限范围
        dataScope := getUserDataScope(c, user.UserId)
        
        // 将数据权限存储到上下文中
        c.Set("dataScope", dataScope)
        
        // 如果是自定义数据权限，还需要获取角色部门IDs
        if dataScope == "2" {
            deptIds := getRoleDeptIds(c, user.UserId)
            c.Set("dataScopeDeptIds", deptIds)
        }
        
        c.Next()
    }
}
```

### 2. 数据库查询过滤

在Service层进行数据库查询时，根据上下文中的数据权限范围，动态添加查询条件。

```go
// dataScopeFilter 数据权限过滤
func (s *BaseService) dataScopeFilter(ctx *gin.Context, db *gorm.DB, deptAlias, userAlias string) *gorm.DB {
    // 获取当前用户
    user := auth.GetCurrentUser(ctx)
    if user == nil {
        return db
    }
    
    // 如果是管理员，不进行数据过滤
    if user.IsAdmin() {
        return db
    }
    
    // 从上下文中获取数据权限范围
    dataScope, _ := ctx.Get("dataScope")
    dataScopeStr, ok := dataScope.(string)
    if !ok {
        return db
    }
    
    // 根据数据范围类型进行过滤
    switch dataScopeStr {
    case "1": // 全部数据权限
        return db
    case "2": // 自定数据权限
        deptIds, _ := ctx.Get("dataScopeDeptIds")
        deptIdList, ok := deptIds.([]uint64)
        if !ok {
            return db
        }
        if deptAlias != "" {
            return db.Where(fmt.Sprintf("%s.dept_id IN (?)", deptAlias), deptIdList)
        }
        return db.Where("dept_id IN (?)", deptIdList)
    case "3": // 本部门数据权限
        if deptAlias != "" {
            return db.Where(fmt.Sprintf("%s.dept_id = ?", deptAlias), user.DeptId)
        }
        return db.Where("dept_id = ?", user.DeptId)
    case "4": // 本部门及以下数据权限
        // 查询部门及其所有子部门
        dept, err := s.GetDeptById(user.DeptId)
        if err != nil {
            return db
        }
        
        if deptAlias != "" {
            return db.Where(fmt.Sprintf("%s.dept_id = ? OR %s.ancestors LIKE ?", deptAlias, deptAlias), 
                user.DeptId, "%,"+strconv.FormatUint(user.DeptId, 10)+",%")
        }
        return db.Where("dept_id = ? OR ancestors LIKE ?", 
            user.DeptId, "%,"+strconv.FormatUint(user.DeptId, 10)+",%")
    case "5": // 仅本人数据权限
        if userAlias != "" {
            return db.Where(fmt.Sprintf("%s.create_by = ?", userAlias), user.UserName)
        }
        return db.Where("create_by = ?", user.UserName)
    default:
        return db
    }
}
```

### 3. 公共查询基类

创建一个基础服务类，封装数据权限过滤逻辑，让其他服务继承使用。

```go
// BaseService 基础服务
type BaseService struct {
    DB *gorm.DB
}

// applyDataScope 应用数据权限
func (s *BaseService) ApplyDataScope(ctx *gin.Context, db *gorm.DB, deptAlias, userAlias string) *gorm.DB {
    return s.dataScopeFilter(ctx, db, deptAlias, userAlias)
}

// GetDeptById 获取部门
func (s *BaseService) GetDeptById(deptId uint64) (*models.SysDept, error) {
    var dept models.SysDept
    if err := s.DB.Where("dept_id = ?", deptId).First(&dept).Error; err != nil {
        return nil, err
    }
    return &dept, nil
}
```

### 4. 使用示例

在具体的业务服务中，使用基础服务提供的数据权限过滤功能。

```go
// UserService 用户服务
type UserService struct {
    BaseService
}

// SelectUserList 查询用户列表
func (s *UserService) SelectUserList(ctx *gin.Context, req request.UserListRequest) ([]*models.SysUser, int64, error) {
    var users []*models.SysUser
    var total int64
    
    // 构建查询
    db := s.DB.Model(&models.SysUser{}).Where("del_flag = ?", "0")
    
    // 添加查询条件
    if req.UserName != "" {
        db = db.Where("user_name like ?", "%"+req.UserName+"%")
    }
    
    if req.Status != "" {
        db = db.Where("status = ?", req.Status)
    }
    
    // 应用数据权限过滤
    db = s.ApplyDataScope(ctx, db, "d", "u")
    
    // 统计总数
    db.Count(&total)
    
    // 分页查询
    if err := db.Preload("Dept").Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).Find(&users).Error; err != nil {
        return nil, 0, err
    }
    
    return users, total, nil
}
```

## 主要接口和方法

### 1. 获取用户数据权限范围

```go
// getUserDataScope 获取用户数据权限范围
func getUserDataScope(ctx context.Context, userId uint64) string {
    // 查询用户角色
    var roles []*models.SysRole
    db := global.DB.Model(&models.SysRole{}).
        Joins("JOIN sys_user_role ur ON ur.role_id = sys_role.role_id").
        Where("ur.user_id = ? AND sys_role.del_flag = '0'", userId).
        Order("sys_role.role_sort")
    
    if err := db.Find(&roles).Error; err != nil {
        return "5" // 默认仅本人数据权限
    }
    
    // 如果用户没有角色，默认仅本人数据权限
    if len(roles) == 0 {
        return "5"
    }
    
    // 按数据范围的优先级排序：全部 > 自定义 > 本部门及以下 > 本部门 > 仅本人
    // 取优先级最高的数据范围
    dataScope := "5"
    for _, role := range roles {
        if role.DataScope == "1" { // 全部数据权限
            return "1"
        }
        
        // 按优先级比较
        if (role.DataScope == "2" && (dataScope == "3" || dataScope == "4" || dataScope == "5")) ||
           (role.DataScope == "4" && (dataScope == "3" || dataScope == "5")) ||
           (role.DataScope == "3" && dataScope == "5") {
            dataScope = role.DataScope
        }
    }
    
    return dataScope
}
```

### 2. 获取角色部门IDs

```go
// getRoleDeptIds 获取角色可访问的部门IDs
func getRoleDeptIds(ctx context.Context, userId uint64) []uint64 {
    var deptIds []uint64
    
    // 查询用户角色关联的部门
    err := global.DB.Model(&models.SysRoleDept{}).
        Select("rd.dept_id").
        Joins("JOIN sys_user_role ur ON ur.role_id = sys_role_dept.role_id").
        Where("ur.user_id = ?", userId).
        Pluck("rd.dept_id", &deptIds).Error
    
    if err != nil {
        return []uint64{}
    }
    
    return deptIds
}
```

## 与原版差异

1. **实现方式**：Java版本使用注解和拦截器，Go版本使用中间件和服务层封装。
2. **数据存储**：数据库表结构和数据权限类型保持一致。
3. **使用方式**：Java版本通过注解声明需要数据权限的方法，Go版本在Service层显式调用权限过滤函数。

## 实现难点

1. **树形部门结构**：处理"本部门及以下"数据权限时，需要正确处理部门的树形结构。
2. **SQL构建**：动态生成数据权限的SQL条件，需要考虑表别名和复杂查询场景。
3. **性能考虑**：数据权限过滤可能会影响查询性能，需要优化查询和添加适当的索引。
4. **缓存策略**：频繁查询用户角色和数据权限会增加数据库负担，可以考虑适当缓存。

## 优化建议

1. **缓存角色权限**：可以在用户登录时获取并缓存其数据权限范围，减少数据库查询。
2. **预处理数据**：对于常用的数据权限过滤条件，可以提前准备好相关数据，如部门树结构。
3. **批量查询优化**：在进行大量数据查询时，先获取符合权限的ID列表，再一次性查询详细数据。
4. **索引优化**：在相关表的过滤字段上添加索引，如`dept_id`、`create_by`等。 