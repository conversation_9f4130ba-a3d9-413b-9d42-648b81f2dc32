# 若依系统前后端一体化运行指南

## 项目结构
- 前端：Vue3 + Element Plus + Vite (位于项目根目录)
- 后端：Spring Boot + MyBatis (位于 `ruoyi-java` 目录)

## 快速启动指南（使用自动化脚本）

1. **配置数据库**
   - 确保安装了MySQL 5.7或更高版本
   - 运行 `setup-database.bat`，按照提示输入MySQL用户名和密码
   - 此脚本将自动创建数据库、导入数据并配置数据库连接

2. **启动后端**
   - 运行 `run-backend.bat`
   - 此脚本将检查环境、编译项目并启动后端服务

3. **启动前端**
   - 运行 `npm run dev`
   - 前端默认运行在 http://localhost:80 或 http://localhost:81

4. **系统访问**
   - 访问地址：http://localhost:80 或 http://localhost:81
   - 默认账号：admin
   - 默认密码：admin123

## 手动配置步骤

### 1. 数据库配置
1. 安装 MySQL 5.7 或更高版本
2. 创建数据库
   ```sql
   CREATE DATABASE `ry-vue` CHARACTER SET 'utf8mb4';
   ```
3. 导入数据库脚本
   - 导入基础数据：`ruoyi-java/sql/ry_20250522.sql`
   - 导入定时任务表：`ruoyi-java/sql/quartz.sql`

### 2. 修改后端配置
1. 修改数据库连接信息 (`ruoyi-java/ruoyi-admin/src/main/resources/application-druid.yml`)
   ```yaml
   spring:
     datasource:
       druid:
         master:
           url: ***********************************************************************************************************************************************
           username: root  # 修改为您的数据库用户名
           password: password  # 修改为您的数据库密码
   ```

2. 如需修改其他配置，请查看 `ruoyi-java/ruoyi-admin/src/main/resources/application.yml`

### 3. 运行后端
1. 确保已安装 JDK 8 或更高版本
2. 确保已安装 Maven 3 或更高版本
3. 执行以下命令编译并运行后端

   ```bash
   # 进入后端根目录
   cd ruoyi-java
   
   # 编译
   mvn clean package -DskipTests
   
   # 运行
   java -jar ruoyi-admin/target/ruoyi-admin.jar
   ```

### 4. 运行前端
1. 前端已配置好代理，默认连接到 `http://localhost:8080`
2. 执行以下命令启动前端

   ```bash
   # 在项目根目录执行
   npm run dev
   ```

## 常见问题

1. **数据库连接错误**
   - 确保MySQL服务已启动
   - 确保已创建名为"ry-vue"的数据库
   - 确保数据库用户名和密码正确

2. **前端无法连接后端**
   - 确保后端服务正常启动在8080端口
   - 查看后端启动日志是否有错误
   - 检查前端控制台是否有网络错误

3. **修改后端端口**
   - 修改 `ruoyi-java/ruoyi-admin/src/main/resources/application.yml` 中的端口配置
   - 同时需要修改前端代理配置（vite.config.js中的baseUrl）

4. **Maven或Java环境问题**
   - 确保安装了正确版本的JDK（推荐JDK 8）
   - 确保Maven正确配置（可使用D:\tools\apache-maven-3.9.10） 