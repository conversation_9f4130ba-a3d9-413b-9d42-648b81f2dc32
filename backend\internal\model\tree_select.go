package model

// TreeSelect 下拉树结构
type TreeSelect struct {
	Id       uint64        `json:"id"`       // 节点ID
	Label    string        `json:"label"`    // 节点名称
	Children []*TreeSelect `json:"children"` // 子节点
	Disabled bool          `json:"disabled"` // 是否禁用
}

// NewTreeSelectFromDept 从部门创建树选择结构
func NewTreeSelectFromDept(dept *SysDept) *TreeSelect {
	children := make([]*TreeSelect, 0)
	if dept.Children != nil {
		for _, child := range dept.Children {
			children = append(children, NewTreeSelectFromDept(child))
		}
	}

	return &TreeSelect{
		Id:       dept.DeptId,
		Label:    dept.DeptName,
		Disabled: dept.Status == "1", // 1表示停用
		Children: children,
	}
}

// NewTreeSelectFromMenu 从菜单创建树选择结构
func NewTreeSelectFromMenu(menu *SysMenu) *TreeSelect {
	children := make([]*TreeSelect, 0)
	if menu.Children != nil {
		for _, child := range menu.Children {
			children = append(children, NewTreeSelectFromMenu(child))
		}
	}

	return &TreeSelect{
		Id:       menu.MenuId,
		Label:    menu.MenuName,
		Children: children,
	}
}
