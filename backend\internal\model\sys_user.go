package model

import (
	"time"
)

// SysUser 用户表 sys_user
type SysUser struct {
	BaseEntity              // 包含基础字段
	UserId        uint64    `json:"userId" gorm:"column:user_id;primaryKey;comment:用户ID"`
	DeptId        uint64    `json:"deptId" gorm:"column:dept_id;comment:部门ID"`
	UserName      string    `json:"userName" gorm:"column:user_name;type:varchar(30);not null;comment:用户账号"`
	NickName      string    `json:"nickName" gorm:"column:nick_name;type:varchar(30);not null;comment:用户昵称"`
	UserType      string    `json:"userType" gorm:"column:user_type;type:varchar(2);default:00;comment:用户类型（00系统用户）"`
	Email         string    `json:"email" gorm:"column:email;type:varchar(50);comment:用户邮箱"`
	Phonenumber   string    `json:"phonenumber" gorm:"column:phonenumber;type:varchar(11);comment:手机号码"`
	Sex           string    `json:"sex" gorm:"column:sex;type:char(1);default:0;comment:用户性别（0男 1女 2未知）"`
	Avatar        string    `json:"avatar" gorm:"column:avatar;type:varchar(100);comment:头像地址"`
	Password      string    `json:"password" gorm:"column:password;type:varchar(100);comment:密码"`
	Salt          string    `json:"salt" gorm:"column:salt;type:varchar(20);comment:盐加密"`
	Status        string    `json:"status" gorm:"column:status;type:char(1);default:0;comment:帐号状态（0正常 1停用）"`
	LoginIp       string    `json:"loginIp" gorm:"column:login_ip;type:varchar(128);comment:最后登录IP"`
	LoginDate     time.Time `json:"loginDate" gorm:"column:login_date;comment:最后登录时间"`
	PwdUpdateDate time.Time `json:"pwdUpdateDate" gorm:"column:pwd_update_date;comment:密码最后更新时间"`

	// 关联关系
	Dept  *SysDept   `json:"dept" gorm:"foreignKey:DeptId;references:DeptId"`
	Roles []*SysRole `json:"roles" gorm:"-"` // 角色列表，不在数据库存储，通过关联表获取
	Posts []*SysPost `json:"posts" gorm:"-"` // 岗位列表，不在数据库存储，通过关联表获取

	// 非数据库字段
	RoleIds []uint64 `json:"roleIds" gorm:"-"` // 角色ID列表
	PostIds []uint64 `json:"postIds" gorm:"-"` // 岗位ID列表
	RoleId  uint64   `json:"roleId" gorm:"-"`  // 角色ID
}

// TableName 设置表名
func (SysUser) TableName() string {
	return "sys_user"
}

// IsAdmin 判断是否是管理员
func (u *SysUser) IsAdmin() bool {
	return u.UserId == 1
}

// 用户与角色关联表 sys_user_role
type SysUserRole struct {
	UserId uint64 `json:"userId" gorm:"column:user_id;primaryKey;comment:用户ID"`
	RoleId uint64 `json:"roleId" gorm:"column:role_id;primaryKey;comment:角色ID"`
}

// TableName 设置表名
func (SysUserRole) TableName() string {
	return "sys_user_role"
}

// 用户与岗位关联表 sys_user_post
type SysUserPost struct {
	UserId uint64 `json:"userId" gorm:"column:user_id;primaryKey;comment:用户ID"`
	PostId uint64 `json:"postId" gorm:"column:post_id;primaryKey;comment:岗位ID"`
}

// TableName 设置表名
func (SysUserPost) TableName() string {
	return "sys_user_post"
}
