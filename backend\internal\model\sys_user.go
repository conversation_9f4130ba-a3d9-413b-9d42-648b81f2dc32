package model

import (
	"time"
)

// SysUser 用户表 sys_user
type SysUser struct {
	BaseEntity        // 包含基础字段
	UserId     uint64 `json:"userId" gorm:"column:user_id;primaryKey;comment:用户ID"`
	DeptId     uint64 `json:"deptId" gorm:"column:dept_id;comment:部门ID"`
	UserName   string `json:"userName" gorm:"column:user_name;type:varchar(30);not null;comment:用户账号"`
	NickName   string `json:"nickName" gorm:"column:nick_name;type:varchar(30);not null;comment:用户昵称"`

	Email       string `json:"email" gorm:"column:email;type:varchar(50);comment:用户邮箱"`
	Phonenumber string `json:"phonenumber" gorm:"column:phonenumber;type:varchar(11);comment:手机号码"`
	Sex         string `json:"sex" gorm:"column:sex;type:char(1);default:0;comment:用户性别（0男 1女 2未知）"`
	Avatar      string `json:"avatar" gorm:"column:avatar;type:varchar(100);comment:头像地址"`
	Password    string `json:"password" gorm:"column:password;type:varchar(100);comment:密码"`

	Status        string    `json:"status" gorm:"column:status;type:char(1);default:0;comment:帐号状态（0正常 1停用）"`
	DelFlag       string    `json:"delFlag" gorm:"column:del_flag;type:char(1);default:0;comment:删除标志（0代表存在 2代表删除）"`
	LoginIp       string    `json:"loginIp" gorm:"column:login_ip;type:varchar(128);comment:最后登录IP"`
	LoginDate     time.Time `json:"loginDate" gorm:"column:login_date;comment:最后登录时间"`
	PwdUpdateDate time.Time `json:"pwdUpdateDate" gorm:"column:pwd_update_date;comment:密码最后更新时间"`

	// 关联关系
	Dept  *SysDept   `json:"dept" gorm:"foreignKey:DeptId;references:DeptId"`
	Roles []*SysRole `json:"roles" gorm:"many2many:sys_user_role;foreignKey:UserId;joinForeignKey:UserId;References:RoleId;JoinReferences:RoleId"` // 角色列表
	Posts []*SysPost `json:"posts" gorm:"many2many:sys_user_post;foreignKey:UserId;joinForeignKey:UserId;References:PostId;JoinReferences:PostId"` // 岗位列表

	// 非数据库字段
	RoleIds []uint64 `json:"roleIds" gorm:"-"` // 角色ID列表（保持 Go 风格的切片）
	PostIds []uint64 `json:"postIds" gorm:"-"` // 岗位ID列表（保持 Go 风格的切片）
	RoleId  uint64   `json:"roleId" gorm:"-"`  // 角色ID
}

// TableName 设置表名
func (SysUser) TableName() string {
	return "sys_user"
}

// IsAdmin 判断是否是管理员
func (u *SysUser) IsAdmin() bool {
	return u.UserId == 1
}
