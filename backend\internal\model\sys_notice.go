package model

// SysNotice 通知公告表 sys_notice
type SysNotice struct {
	BaseEntity           // 修改为 BaseEntity 以保持与 Java 版本一致
	NoticeId      uint64 `json:"noticeId" gorm:"column:notice_id;primaryKey;comment:公告ID"`
	NoticeTitle   string `json:"noticeTitle" gorm:"column:notice_title;type:varchar(50);not null;comment:公告标题"`
	NoticeType    string `json:"noticeType" gorm:"column:notice_type;type:char(1);not null;comment:公告类型（1通知 2公告）"`
	NoticeContent string `json:"noticeContent" gorm:"column:notice_content;type:varchar(2000);comment:公告内容"`
	Status        string `json:"status" gorm:"column:status;type:char(1);default:0;comment:公告状态（0正常 1关闭）"`
}

// TableName 设置表名
func (SysNotice) TableName() string {
	return "sys_notice"
}
