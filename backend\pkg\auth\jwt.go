package auth

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/ruoyi-go/config"
)

// Claims JWT载荷信息
type Claims struct {
	UserId      uint64   `json:"userId"`
	Username    string   `json:"username"`
	RoleIds     []uint64 `json:"roleIds"`
	Permissions []string `json:"permissions"`
	jwt.RegisteredClaims
}

// GenerateToken 生成JWT令牌
func GenerateToken(userId uint64, username string, roleIds []uint64, permissions []string) (string, error) {
	// 设置JWT过期时间
	expireTime := time.Now().Add(time.Minute * config.AppConfig.JWT.ExpireTime)

	// 创建JWT载荷
	claims := Claims{
		UserId:      userId,
		Username:    username,
		RoleIds:     roleIds,
		Permissions: permissions,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   username,
			Issuer:    "ruoyi-go",
		},
	}

	// 创建JWT令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名JWT令牌
	return token.SignedString([]byte(config.AppConfig.JWT.Secret))
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*Claims, error) {
	// 解析JWT令牌
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.AppConfig.JWT.Secret), nil
	})

	// 检查JWT令牌是否有效
	if err != nil {
		return nil, err
	}

	// 检查JWT令牌的载荷
	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// RefreshToken 刷新JWT令牌
func RefreshToken(token string) (string, error) {
	// 解析旧令牌
	claims, err := ParseToken(token)
	if err != nil {
		return "", err
	}

	// 创建新令牌
	return GenerateToken(claims.UserId, claims.Username, claims.RoleIds, claims.Permissions)
}
