package model

import (
	"time"
)

// SysOperLog 操作日志记录表 sys_oper_log
type SysOperLog struct {
	BaseEntity
	OperId        uint64    `json:"operId" gorm:"column:oper_id;primaryKey;comment:日志主键"`
	Title         string    `json:"title" gorm:"column:title;type:varchar(50);comment:模块标题"`
	BusinessType  int       `json:"businessType" gorm:"column:business_type;default:0;comment:业务类型（0其它 1新增 2修改 3删除）"`
	Method        string    `json:"method" gorm:"column:method;type:varchar(100);comment:方法名称"`
	RequestMethod string    `json:"requestMethod" gorm:"column:request_method;type:varchar(10);comment:请求方式"`
	OperatorType  int       `json:"operatorType" gorm:"column:operator_type;default:0;comment:操作类别（0其它 1后台用户 2手机端用户）"`
	OperName      string    `json:"operName" gorm:"column:oper_name;type:varchar(50);comment:操作人员"`
	DeptName      string    `json:"deptName" gorm:"column:dept_name;type:varchar(50);comment:部门名称"`
	OperUrl       string    `json:"operUrl" gorm:"column:oper_url;type:varchar(255);comment:请求URL"`
	OperIp        string    `json:"operIp" gorm:"column:oper_ip;type:varchar(50);comment:主机地址"`
	OperLocation  string    `json:"operLocation" gorm:"column:oper_location;type:varchar(255);comment:操作地点"`
	OperParam     string    `json:"operParam" gorm:"column:oper_param;type:varchar(2000);comment:请求参数"`
	JsonResult    string    `json:"jsonResult" gorm:"column:json_result;type:varchar(2000);comment:返回参数"`
	Status        int       `json:"status" gorm:"column:status;default:0;comment:操作状态（0正常 1异常）"`
	ErrorMsg      string    `json:"errorMsg" gorm:"column:error_msg;type:varchar(2000);comment:错误消息"`
	OperTime      time.Time `json:"operTime" gorm:"column:oper_time;comment:操作时间"`
	CostTime      int64     `json:"costTime" gorm:"column:cost_time;comment:消耗时间"`

	// 非数据库字段
	BusinessTypes []int `json:"businessTypes" gorm:"-"` // 业务类型数组
}

// TableName 设置表名
func (SysOperLog) TableName() string {
	return "sys_oper_log"
}
