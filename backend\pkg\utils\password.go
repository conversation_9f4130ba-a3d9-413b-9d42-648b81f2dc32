package utils

import (
	"crypto/md5"
	"encoding/hex"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

// 盐值常量，用于增强密码安全性
const (
	PasswordSalt = "RuoYi-Go-Salt"
)

// EncryptPassword 使用bcrypt加密密码
func EncryptPassword(password string) string {
	// 先使用MD5加密，增加混淆
	hashedPassword := MD5(password + PasswordSalt)

	// 使用bcrypt加密，提高安全性
	hash, err := bcrypt.GenerateFromPassword([]byte(hashedPassword), bcrypt.DefaultCost)
	if err != nil {
		return ""
	}

	return string(hash)
}

// MatchPassword 验证密码是否匹配
func MatchPassword(password, hashedPassword string) bool {
	// 先使用MD5加密，增加混淆
	hashedInput := MD5(password + PasswordSalt)

	// 使用bcrypt验证密码
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(hashedInput))
	return err == nil
}

// MD5 计算字符串的MD5值
func MD5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// IsPasswordValid 检查密码是否符合复杂度要求
// 默认要求密码至少8位，包含字母和数字
func IsPasswordValid(password string) bool {
	if len(password) < 8 {
		return false
	}

	var hasLetter, hasNumber bool
	for _, c := range password {
		if (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') {
			hasLetter = true
		} else if c >= '0' && c <= '9' {
			hasNumber = true
		}

		if hasLetter && hasNumber {
			return true
		}
	}

	return false
}

// IsAdminUserName 判断是否是管理员用户名
func IsAdminUserName(username string) bool {
	return strings.ToLower(username) == "admin"
}
