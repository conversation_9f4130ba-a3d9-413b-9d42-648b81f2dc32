package model

import "strings"

// SysCache 缓存信息
type SysCache struct {
	CacheName  string `json:"cacheName"`  // 缓存名称
	CacheKey   string `json:"cacheKey"`   // 缓存键名
	CacheValue string `json:"cacheValue"` // 缓存内容
	Remark     string `json:"remark"`     // 备注
}

// NewSysCache 创建缓存信息（缓存名称和备注）
func NewSysCache(cacheName, remark string) *SysCache {
	return &SysCache{
		CacheName: cacheName,
		Remark:    remark,
	}
}

// NewSysCacheWithValue 创建缓存信息（完整信息）
func NewSysCacheWithValue(cacheName, cacheKey, cacheValue string) *SysCache {
	return &SysCache{
		CacheName:  strings.ReplaceAll(cacheName, ":", ""),
		CacheKey:   strings.ReplaceAll(cacheKey, cacheName, ""),
		CacheValue: cacheValue,
	}
}
