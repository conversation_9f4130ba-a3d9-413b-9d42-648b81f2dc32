package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// 常用响应状态码
const (
	SUCCESS      = 200
	ERROR        = 500
	UNAUTHORIZED = 401
	FORBIDDEN    = 403
	NOT_FOUND    = 404
)

// Response 统一响应结构体
type Response struct {
	Code int         `json:"code"` // 状态码
	Msg  string      `json:"msg"`  // 提示信息
	Data interface{} `json:"data"` // 数据内容
}

// Success 响应成功
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code: SUCCESS,
		Msg:  "操作成功",
		Data: data,
	})
}

// SuccessMsg 响应成功，自定义消息
func SuccessMsg(c *gin.Context, message string) {
	c.JSON(http.StatusOK, Response{
		Code: SUCCESS,
		Msg:  message,
		Data: nil,
	})
}

// SuccessWithMsg 响应成功，自定义消息和数据
func SuccessWithMsg(c *gin.Context, message string, data interface{}) {
	c.<PERSON><PERSON><PERSON>(http.StatusOK, Response{
		Code: SUCCESS,
		Msg:  message,
		Data: data,
	})
}

// Error 响应错误
func Error(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, Response{
		Code: code,
		Msg:  message,
		Data: nil,
	})
}

// ErrorDefault 默认错误响应
func ErrorDefault(c *gin.Context) {
	Error(c, ERROR, "系统错误，请联系管理员")
}

// ErrorMsg 使用默认错误码的错误响应
func ErrorMsg(c *gin.Context, message string) {
	Error(c, ERROR, message)
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "未授权"
	}
	Error(c, UNAUTHORIZED, message)
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	if message == "" {
		message = "禁止访问"
	}
	Error(c, FORBIDDEN, message)
}

// NotFound 资源不存在响应
func NotFound(c *gin.Context, message string) {
	if message == "" {
		message = "资源不存在"
	}
	Error(c, NOT_FOUND, message)
}

// Paginate 分页响应
func Paginate(c *gin.Context, list interface{}, total int64) {
	c.JSON(http.StatusOK, Response{
		Code: SUCCESS,
		Msg:  "查询成功",
		Data: gin.H{
			"rows":  list,
			"total": total,
		},
	})
}

// ParamError 参数错误响应
func ParamError(c *gin.Context, message string) {
	if message == "" {
		message = "参数错误"
	}
	Error(c, ERROR, message)
}
