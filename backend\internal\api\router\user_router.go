package router

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi-go/internal/api/controller"
	"github.com/ruoyi-go/pkg/middleware"
)

// RegisterUserRoutes 注册用户管理相关路由
func RegisterUserRoutes(r *gin.RouterGroup, userController *controller.UserController) {
	// 用户管理路由
	userRouter := r.Group("/system/user")
	{
		// 用户管理API
		userRouter.GET("/list", middleware.Auth(), middleware.HasPermission("system:user:list"), userController.List)
		userRouter.GET("/:userId", middleware.Auth(), middleware.HasPermission("system:user:query"), userController.GetInfo)
		userRouter.POST("", middleware.Auth(), middleware.HasPermission("system:user:add"), userController.Add)
		userRouter.PUT("", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.Update)
		userRouter.DELETE("/:userIds", middleware.Auth(), middleware.HasPermission("system:user:remove"), userController.Delete)
		userRouter.PUT("/resetPwd", middleware.Auth(), middleware.HasPermission("system:user:resetPwd"), userController.ResetPwd)
		userRouter.PUT("/changeStatus", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.ChangeStatus)
		userRouter.GET("/authRole/:userId", middleware.Auth(), middleware.HasPermission("system:user:query"), userController.AuthRole)
		userRouter.PUT("/authRole", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.InsertAuthRole)

		// 个人信息相关路由
		profileRouter := userRouter.Group("/profile")
		{
			profileRouter.GET("", middleware.Auth(), userController.Profile)
			profileRouter.PUT("", middleware.Auth(), userController.UpdateProfile)
			profileRouter.PUT("/updatePwd", middleware.Auth(), userController.UpdatePwd)
			profileRouter.POST("/avatar", middleware.Auth(), userController.UploadAvatar)
		}
	}
}
