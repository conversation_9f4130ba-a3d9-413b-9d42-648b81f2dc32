package model

// SysRole 角色表 sys_role
type SysRole struct {
	BaseEntity
	RoleId            uint64 `json:"roleId" gorm:"column:role_id;primaryKey;comment:角色ID"`
	RoleName          string `json:"roleName" gorm:"column:role_name;type:varchar(30);not null;comment:角色名称"`
	RoleKey           string `json:"roleKey" gorm:"column:role_key;type:varchar(100);not null;comment:角色权限字符串"`
	RoleSort          int    `json:"roleSort" gorm:"column:role_sort;not null;comment:显示顺序"`
	DataScope         string `json:"dataScope" gorm:"column:data_scope;type:char(1);default:1;comment:数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限）"`
	MenuCheckStrictly bool   `json:"menuCheckStrictly" gorm:"column:menu_check_strictly;default:1;comment:菜单树选择项是否关联显示"`
	DeptCheckStrictly bool   `json:"deptCheckStrictly" gorm:"column:dept_check_strictly;default:1;comment:部门树选择项是否关联显示"`
	Status            string `json:"status" gorm:"column:status;type:char(1);not null;comment:角色状态（0正常 1停用）"`

	// 非数据库字段
	MenuIds     []uint64 `json:"menuIds" gorm:"-"`     // 菜单ID列表
	DeptIds     []uint64 `json:"deptIds" gorm:"-"`     // 部门ID列表
	Flag        bool     `json:"flag" gorm:"-"`        // 用户是否拥有该角色标识
	Permissions []string `json:"permissions" gorm:"-"` // 角色菜单权限
}

// TableName 设置表名
func (SysRole) TableName() string {
	return "sys_role"
}

// IsAdmin 判断是否是管理员角色
func (r *SysRole) IsAdmin() bool {
	return r.RoleId == 1
}

// 角色和菜单关联表 sys_role_menu
type SysRoleMenu struct {
	RoleId uint64 `json:"roleId" gorm:"column:role_id;primaryKey;comment:角色ID"`
	MenuId uint64 `json:"menuId" gorm:"column:menu_id;primaryKey;comment:菜单ID"`
}

// TableName 设置表名
func (SysRoleMenu) TableName() string {
	return "sys_role_menu"
}

// 角色和部门关联表 sys_role_dept
type SysRoleDept struct {
	RoleId uint64 `json:"roleId" gorm:"column:role_id;primaryKey;comment:角色ID"`
	DeptId uint64 `json:"deptId" gorm:"column:dept_id;primaryKey;comment:部门ID"`
}

// TableName 设置表名
func (SysRoleDept) TableName() string {
	return "sys_role_dept"
}
