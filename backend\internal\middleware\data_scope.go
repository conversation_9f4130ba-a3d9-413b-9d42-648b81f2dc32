package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/ruoyi-go/internal/model"
	"github.com/ruoyi-go/internal/utils"
)

// 数据权限常量
const (
	// 全部数据权限
	DATA_SCOPE_ALL = "1"
	// 自定数据权限
	DATA_SCOPE_CUSTOM = "2"
	// 部门数据权限
	DATA_SCOPE_DEPT = "3"
	// 部门及以下数据权限
	DATA_SCOPE_DEPT_AND_CHILD = "4"
	// 仅本人数据权限
	DATA_SCOPE_SELF = "5"
	// 数据权限过滤关键字
	DATA_SCOPE = "dataScope"
)

// DataScope 数据权限注解结构体
type DataScope struct {
	DeptAlias  string // 部门别名
	UserAlias  string // 用户别名
	Permission string // 权限字符
}

// DataScopeFilter 数据范围过滤
// user 当前用户
// deptAlias 部门别名
// userAlias 用户别名
// permission 权限字符
// entity 查询参数
func DataScopeFilter(c *gin.Context, user *model.SysUser, deptAlias, userAlias, permission string, entity *model.BaseEntity) {
	// 如果是超级管理员，则不过滤数据
	if user.IsAdmin() {
		return
	}

	sqlString := ""
	conditions := make([]string, 0)
	scopeCustomIds := make([]string, 0)

	// 获取当前用户的角色列表
	var roles []*model.SysRole
	// 这里假设已经有了用户的角色列表
	// 在实际使用时，需要从上下文中获取用户的角色列表
	// 或者从数据库中查询
	// roles = getUserRoles(c, user.UserId)
	if len(roles) == 0 {
		// 如果没有角色，则限制不能查看任何数据
		if entity != nil && entity.Params != nil {
			entity.Params[DATA_SCOPE] = " AND (" + deptAlias + ".dept_id = 0)"
		}
		return
	}

	// 查找自定义数据权限的角色ID
	for _, role := range roles {
		if DATA_SCOPE_CUSTOM == role.DataScope && role.Status == "0" {
			// 在实际使用时，需要判断角色的权限与传入的权限是否匹配
			// 这里简化处理，认为匹配
			scopeCustomIds = append(scopeCustomIds, utils.Uint64ToString(role.RoleId))
		}
	}

	// 根据角色的数据范围进行过滤
	for _, role := range roles {
		dataScope := role.DataScope
		// 跳过已经处理过的数据范围和停用的角色
		alreadyAdded := false
		for _, addedScope := range conditions {
			if addedScope == dataScope {
				alreadyAdded = true
				break
			}
		}
		if alreadyAdded || role.Status == "1" {
			continue
		}

		// 在实际使用时，需要判断角色的权限与传入的权限是否匹配
		// 这里简化处理，认为匹配

		// 根据数据权限范围生成SQL
		if DATA_SCOPE_ALL == dataScope {
			// 全部数据权限，不需要添加过滤条件
			sqlString = ""
			conditions = append(conditions, dataScope)
			break
		} else if DATA_SCOPE_CUSTOM == dataScope {
			// 自定义数据权限
			if len(scopeCustomIds) > 1 {
				// 多个自定数据权限使用in查询，避免多次拼接
				sqlString += " OR " + deptAlias + ".dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id in (" + utils.StringsJoin(scopeCustomIds, ",") + ") )"
			} else if len(scopeCustomIds) == 1 {
				sqlString += " OR " + deptAlias + ".dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = " + scopeCustomIds[0] + " )"
			}
			conditions = append(conditions, dataScope)
		} else if DATA_SCOPE_DEPT == dataScope {
			// 本部门数据权限
			sqlString += " OR " + deptAlias + ".dept_id = " + utils.Uint64ToString(user.DeptId)
			conditions = append(conditions, dataScope)
		} else if DATA_SCOPE_DEPT_AND_CHILD == dataScope {
			// 本部门及以下数据权限
			sqlString += " OR " + deptAlias + ".dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = " + utils.Uint64ToString(user.DeptId) + " OR find_in_set( " + utils.Uint64ToString(user.DeptId) + " , ancestors ) )"
			conditions = append(conditions, dataScope)
		} else if DATA_SCOPE_SELF == dataScope {
			// 仅本人数据权限
			if userAlias != "" {
				sqlString += " OR " + userAlias + ".user_id = " + utils.Uint64ToString(user.UserId)
			} else {
				// 数据权限为仅本人且没有userAlias别名不查询任何数据
				sqlString += " OR " + deptAlias + ".dept_id = 0"
			}
			conditions = append(conditions, dataScope)
		}
	}

	// 如果没有匹配到任何条件，限制不能查看任何数据
	if len(conditions) == 0 {
		sqlString += " OR " + deptAlias + ".dept_id = 0"
	}

	// 设置数据权限过滤条件
	if sqlString != "" && entity != nil && entity.Params != nil {
		entity.Params[DATA_SCOPE] = " AND (" + sqlString[4:] + ")"
	}
}

// ClearDataScope 清除数据权限过滤条件
func ClearDataScope(entity *model.BaseEntity) {
	if entity != nil && entity.Params != nil {
		entity.Params[DATA_SCOPE] = ""
	}
}
