package com.ruoyi.common.utils;

import java.security.Security;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Properties;

/**
 * SQL Server连接测试工具类
 */
public class SqlServerConnectionTest {

    /**
     * 测试SQL Server连接
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            System.out.println("开始测试SQL Server连接...");
            
            // 设置TLS协议版本
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            
            // 禁用TLS协议版本限制
            Security.setProperty("jdk.tls.disabledAlgorithms", "");
            Security.setProperty("crypto.policy", "unlimited");
            
            // 加载驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("SQL Server驱动加载成功");
            
            // 连接参数
            String url = "******************************************************************************************************************************************************************;";
            String user = "sa";
            String password = "F@2233";
            
            // 创建连接属性
            Properties props = new Properties();
            props.setProperty("user", user);
            props.setProperty("password", password);
            props.setProperty("encrypt", "false");
            props.setProperty("trustServerCertificate", "true");
            
            // 输出当前JVM的TLS设置
            System.out.println("当前TLS协议设置: " + System.getProperty("https.protocols"));
            System.out.println("当前JDK TLS客户端协议设置: " + System.getProperty("jdk.tls.client.protocols"));
            System.out.println("当前禁用算法设置: " + Security.getProperty("jdk.tls.disabledAlgorithms"));
            
            // 建立连接
            System.out.println("尝试连接到SQL Server...");
            Connection conn = DriverManager.getConnection(url, props);
            System.out.println("SQL Server连接成功！");
            
            // 执行简单查询
            Statement stmt = conn.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT @@VERSION");
            
            if (rs.next()) {
                System.out.println("SQL Server版本: " + rs.getString(1));
            }
            
            // 关闭资源
            rs.close();
            stmt.close();
            conn.close();
            System.out.println("测试完成，连接已关闭");
            
        } catch (Exception e) {
            System.err.println("SQL Server连接测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 