package model

// RouterVo 路由视图对象
type RouterVo struct {
	Hidden     bool        `json:"hidden"`               // 是否隐藏路由
	Name       string      `json:"name,omitempty"`       // 路由名字
	Path       string      `json:"path"`                 // 路由地址
	Component  string      `json:"component,omitempty"`  // 组件路径
	Query      string      `json:"query,omitempty"`      // 路由参数
	Redirect   string      `json:"redirect,omitempty"`   // 重定向地址
	AlwaysShow bool        `json:"alwaysShow,omitempty"` // 是否显示根菜单
	Meta       *MetaVo     `json:"meta"`                 // 路由元信息
	Children   []*RouterVo `json:"children,omitempty"`   // 子路由
}

// MetaVo 路由元信息
type MetaVo struct {
	Title      string `json:"title"`                // 设置该路由在侧边栏和面包屑中展示的名字
	Icon       string `json:"icon,omitempty"`       // 设置该路由的图标
	NoCache    bool   `json:"noCache,omitempty"`    // 是否缓存
	Link       string `json:"link,omitempty"`       // 外链地址
	ActiveMenu string `json:"activeMenu,omitempty"` // 高亮菜单
	BreadCrumb bool   `json:"breadcrumb,omitempty"` // 是否在面包屑显示
}
