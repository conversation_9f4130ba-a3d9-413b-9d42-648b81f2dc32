package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/ruoyi-go/internal/middleware"
	"github.com/ruoyi-go/internal/model"
	"github.com/ruoyi-go/internal/utils"
	"gorm.io/gorm"
)

// DeptService 部门服务接口
type DeptService interface {
	// 查询部门
	GetDeptById(ctx context.Context, deptId uint64) (*model.SysDept, error)
	ListDepts(ctx context.Context, dept *model.SysDept) ([]*model.SysDept, error)

	// 部门操作
	CreateDept(ctx context.Context, dept *model.SysDept) error
	UpdateDept(ctx context.Context, dept *model.SysDept) error
	DeleteDeptById(ctx context.Context, deptId uint64) error

	// 树形结构
	BuildDeptTree(depts []*model.SysDept) []*model.SysDept
	BuildDeptTreeSelect(depts []*model.SysDept) []*model.TreeSelect

	// 部门下拉
	SelectDeptListByRoleId(ctx context.Context, roleId uint64) ([]uint64, error)

	// 校验
	CheckDeptNameUnique(ctx context.Context, deptId uint64, deptName string, parentId uint64) (bool, error)
	CheckDeptExistUser(ctx context.Context, deptId uint64) (bool, error)
	HasChildByDeptId(ctx context.Context, deptId uint64) (bool, error)
	CheckDeptAllowed(deptId uint64) error
}

// deptServiceImpl 部门服务实现
type deptServiceImpl struct {
	db *gorm.DB
}

// NewDeptService 创建部门服务
func NewDeptService(db *gorm.DB) DeptService {
	return &deptServiceImpl{
		db: db,
	}
}

// GetDeptById 根据部门ID获取部门信息
func (s *deptServiceImpl) GetDeptById(ctx context.Context, deptId uint64) (*model.SysDept, error) {
	var dept model.SysDept
	err := s.db.Where("dept_id = ? AND del_flag = '0'", deptId).First(&dept).Error
	if err != nil {
		return nil, err
	}
	return &dept, nil
}

// ListDepts 查询部门列表，应用数据权限过滤
func (s *deptServiceImpl) ListDepts(ctx context.Context, dept *model.SysDept) ([]*model.SysDept, error) {
	var depts []*model.SysDept

	// 初始化查询条件
	query := s.db.Model(&model.SysDept{}).Where("del_flag = '0'")

	// 添加查询条件
	if dept != nil {
		if dept.DeptName != "" {
			query = query.Where("dept_name like ?", "%"+dept.DeptName+"%")
		}
		if dept.Status != "" {
			query = query.Where("status = ?", dept.Status)
		}
		if dept.DeptId > 0 {
			query = query.Where("dept_id = ?", dept.DeptId)
		}
		if dept.ParentId > 0 {
			query = query.Where("parent_id = ?", dept.ParentId)
		}

		// 应用数据权限过滤
		baseEntity := &model.BaseEntity{}
		if baseEntity.Params == nil {
			baseEntity.Params = make(map[string]interface{})
		}

		// 获取当前登录用户
		user := getUserFromContext(ctx)
		if user != nil {
			// 调用数据权限过滤
			middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)

			// 应用数据权限过滤
			if baseEntity.Params != nil {
				if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
					// 拼接数据权限SQL，需要使用自连接查询
					query = query.Where(dataScope)
				}
			}
		}
	}

	// 排序
	query = query.Order("parent_id, order_num")

	// 查询部门列表
	err := query.Find(&depts).Error
	if err != nil {
		return nil, err
	}

	return depts, nil
}

// CreateDept 创建部门
func (s *deptServiceImpl) CreateDept(ctx context.Context, dept *model.SysDept) error {
	// 校验部门名称是否唯一
	isUnique, err := s.CheckDeptNameUnique(ctx, 0, dept.DeptName, dept.ParentId)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("新增部门'" + dept.DeptName + "'失败，部门名称已存在")
	}

	// 设置祖级列表
	if dept.ParentId > 0 {
		parent, err := s.GetDeptById(ctx, dept.ParentId)
		if err != nil {
			return err
		}
		dept.Ancestors = parent.Ancestors + "," + fmt.Sprintf("%d", parent.DeptId)
	} else {
		dept.Ancestors = "0"
	}

	// 设置创建时间
	dept.CreateTime = time.Now()

	// 创建部门
	return s.db.Create(dept).Error
}

// UpdateDept 修改部门
func (s *deptServiceImpl) UpdateDept(ctx context.Context, dept *model.SysDept) error {
	// 校验部门是否允许操作
	if err := s.CheckDeptAllowed(dept.DeptId); err != nil {
		return err
	}

	// 校验部门名称是否唯一
	isUnique, err := s.CheckDeptNameUnique(ctx, dept.DeptId, dept.DeptName, dept.ParentId)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("修改部门'" + dept.DeptName + "'失败，部门名称已存在")
	}

	// 获取旧的部门信息
	oldDept, err := s.GetDeptById(ctx, dept.DeptId)
	if err != nil {
		return err
	}

	// 如果父级发生变化，需要更新祖级列表
	if oldDept.ParentId != dept.ParentId {
		// 获取新的父级部门
		var newAncestors string
		if dept.ParentId > 0 {
			parent, err := s.GetDeptById(ctx, dept.ParentId)
			if err != nil {
				return err
			}
			newAncestors = parent.Ancestors + "," + fmt.Sprintf("%d", parent.DeptId)
		} else {
			newAncestors = "0"
		}

		// 设置新的祖级列表
		dept.Ancestors = newAncestors

		// 更新所有子部门的祖级列表
		err = s.updateDeptChildren(ctx, dept.DeptId, newAncestors, oldDept.Ancestors)
		if err != nil {
			return err
		}
	}

	// 设置更新时间
	dept.UpdateTime = time.Now()

	// 更新部门信息
	return s.db.Model(&model.SysDept{}).Where("dept_id = ?", dept.DeptId).
		Omit("create_time", "del_flag").Updates(dept).Error
}

// updateDeptChildren 更新子部门祖级列表
func (s *deptServiceImpl) updateDeptChildren(ctx context.Context, deptId uint64, newAncestors, oldAncestors string) error {
	// 查询所有子部门
	var children []*model.SysDept
	err := s.db.Where("del_flag = '0' AND find_in_set(?, ancestors)", deptId).Find(&children).Error
	if err != nil {
		return err
	}

	// 如果没有子部门，直接返回
	if len(children) == 0 {
		return nil
	}

	// 批量更新子部门祖级列表
	return s.db.Transaction(func(tx *gorm.DB) error {
		for _, child := range children {
			// 替换祖级列表中的旧路径为新路径
			newChildAncestors := utils.ReplaceAncestors(child.Ancestors, oldAncestors, newAncestors)

			// 更新子部门祖级列表
			err := tx.Model(&model.SysDept{}).Where("dept_id = ?", child.DeptId).
				Update("ancestors", newChildAncestors).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// DeleteDeptById 删除部门
func (s *deptServiceImpl) DeleteDeptById(ctx context.Context, deptId uint64) error {
	// 校验部门是否允许操作
	if err := s.CheckDeptAllowed(deptId); err != nil {
		return err
	}

	// 校验部门是否存在子部门
	hasChild, err := s.HasChildByDeptId(ctx, deptId)
	if err != nil {
		return err
	}
	if hasChild {
		return errors.New("存在下级部门，不允许删除")
	}

	// 校验部门是否存在用户
	hasUser, err := s.CheckDeptExistUser(ctx, deptId)
	if err != nil {
		return err
	}
	if hasUser {
		return errors.New("部门存在用户，不允许删除")
	}

	// 逻辑删除部门
	return s.db.Model(&model.SysDept{}).Where("dept_id = ?", deptId).
		Update("del_flag", "2").Error
}

// BuildDeptTree 构建部门树结构
func (s *deptServiceImpl) BuildDeptTree(depts []*model.SysDept) []*model.SysDept {
	// 创建根节点映射
	deptMap := make(map[uint64]*model.SysDept)
	// 将所有部门存入映射
	for _, dept := range depts {
		deptMap[dept.DeptId] = dept
	}

	// 构建树形结构
	var trees []*model.SysDept
	for _, dept := range depts {
		// 如果是根节点，加入树列表
		if dept.ParentId == 0 {
			trees = append(trees, dept)
			continue
		}

		// 如果父节点存在，将当前节点加入父节点的子节点列表
		if parent, ok := deptMap[dept.ParentId]; ok {
			if parent.Children == nil {
				parent.Children = make([]*model.SysDept, 0)
			}
			parent.Children = append(parent.Children, dept)
		} else {
			// 如果父节点不存在，作为根节点处理
			trees = append(trees, dept)
		}
	}

	return trees
}

// BuildDeptTreeSelect 构建部门下拉树结构
func (s *deptServiceImpl) BuildDeptTreeSelect(depts []*model.SysDept) []*model.TreeSelect {
	// 构建部门树
	deptTrees := s.BuildDeptTree(depts)

	// 转换为下拉树结构
	return s.buildDeptTreeSelect(deptTrees)
}

// buildDeptTreeSelect 内部方法，构建部门下拉树结构
func (s *deptServiceImpl) buildDeptTreeSelect(depts []*model.SysDept) []*model.TreeSelect {
	var trees []*model.TreeSelect
	for _, dept := range depts {
		var tree model.TreeSelect
		tree.Id = dept.DeptId
		tree.Label = dept.DeptName

		// 递归处理子节点
		if dept.Children != nil && len(dept.Children) > 0 {
			tree.Children = s.buildDeptTreeSelect(dept.Children)
		}

		trees = append(trees, &tree)
	}

	return trees
}

// SelectDeptListByRoleId 根据角色ID查询部门树信息
func (s *deptServiceImpl) SelectDeptListByRoleId(ctx context.Context, roleId uint64) ([]uint64, error) {
	// 查询角色对应的部门ID列表
	var deptIds []uint64
	err := s.db.Model(&model.SysRoleDept{}).
		Where("role_id = ?", roleId).
		Pluck("dept_id", &deptIds).Error

	return deptIds, err
}

// CheckDeptNameUnique 校验部门名称是否唯一
func (s *deptServiceImpl) CheckDeptNameUnique(ctx context.Context, deptId uint64, deptName string, parentId uint64) (bool, error) {
	var dept model.SysDept
	err := s.db.Where("dept_name = ? AND parent_id = ? AND del_flag = '0'", deptName, parentId).First(&dept).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 如果是修改，需要排除自身
	if deptId > 0 && dept.DeptId == deptId {
		return true, nil
	}

	return false, nil
}

// CheckDeptExistUser 校验部门是否存在用户
func (s *deptServiceImpl) CheckDeptExistUser(ctx context.Context, deptId uint64) (bool, error) {
	var count int64
	err := s.db.Model(&model.SysUser{}).Where("dept_id = ? AND del_flag = '0'", deptId).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// HasChildByDeptId 判断部门是否有子部门
func (s *deptServiceImpl) HasChildByDeptId(ctx context.Context, deptId uint64) (bool, error) {
	var count int64
	err := s.db.Model(&model.SysDept{}).Where("parent_id = ? AND del_flag = '0'", deptId).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckDeptAllowed 校验部门是否允许操作
func (s *deptServiceImpl) CheckDeptAllowed(deptId uint64) error {
	if deptId == 100 {
		return errors.New("不允许操作总公司部门")
	}
	return nil
}
