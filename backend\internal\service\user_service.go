package service

import (
	"context"
	"errors"
	"time"

	"github.com/ruoyi-go/internal/model"
	"github.com/ruoyi-go/pkg/utils"
	"gorm.io/gorm"
)

// UserService 用户服务接口
type UserService interface {
	// 基础查询
	GetUserById(ctx context.Context, userId uint64) (*model.SysUser, error)
	GetUserByUsername(ctx context.Context, username string) (*model.SysUser, error)
	ListUsers(ctx context.Context, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error)

	// 用户操作
	CreateUser(ctx context.Context, user *model.SysUser) error
	UpdateUser(ctx context.Context, user *model.SysUser) error
	DeleteUserByIds(ctx context.Context, userIds []uint64) error
	ResetUserPassword(ctx context.Context, userId uint64, password string) error
	UpdateUserStatus(ctx context.Context, userId uint64, status string) error

	// 角色岗位关系
	GetUserRoles(ctx context.Context, userId uint64) ([]*model.SysRole, error)
	GetUserPosts(ctx context.Context, userId uint64) ([]*model.SysPost, error)
	AssignUserRoles(ctx context.Context, userId uint64, roleIds []uint64) error

	// 个人信息
	UpdateUserProfile(ctx context.Context, user *model.SysUser) error
	UpdateUserPassword(ctx context.Context, userId uint64, oldPassword, newPassword string) error
	UpdateUserAvatar(ctx context.Context, userId uint64, avatar string) error

	// 其他操作
	CheckUserNameUnique(ctx context.Context, userId uint64, userName string) (bool, error)
	CheckPhoneUnique(ctx context.Context, userId uint64, phone string) (bool, error)
	CheckEmailUnique(ctx context.Context, userId uint64, email string) (bool, error)
}

// userServiceImpl 用户服务实现
type userServiceImpl struct {
	db *gorm.DB
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB) UserService {
	return &userServiceImpl{
		db: db,
	}
}

// GetUserById 根据用户ID获取用户信息
func (s *userServiceImpl) GetUserById(ctx context.Context, userId uint64) (*model.SysUser, error) {
	var user model.SysUser
	err := s.db.Where("user_id = ? AND del_flag = '0'", userId).First(&user).Error
	if err != nil {
		return nil, err
	}

	// 查询部门信息
	if user.DeptId > 0 {
		var dept model.SysDept
		s.db.Where("dept_id = ? AND del_flag = '0'", user.DeptId).First(&dept)
		user.Dept = &dept
	}

	// 查询角色信息
	roles, _ := s.GetUserRoles(ctx, userId)
	user.Roles = roles

	// 查询岗位信息
	posts, _ := s.GetUserPosts(ctx, userId)
	user.Posts = posts

	return &user, nil
}

// GetUserByUsername 根据用户名获取用户信息
func (s *userServiceImpl) GetUserByUsername(ctx context.Context, username string) (*model.SysUser, error) {
	var user model.SysUser
	err := s.db.Where("user_name = ? AND del_flag = '0'", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// ListUsers 查询用户列表
func (s *userServiceImpl) ListUsers(ctx context.Context, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error) {
	var users []*model.SysUser
	var total int64

	query := s.db.Model(&model.SysUser{}).Where("del_flag = '0'")

	// 添加查询条件
	if user != nil {
		if user.UserName != "" {
			query = query.Where("user_name like ?", "%"+user.UserName+"%")
		}
		if user.Status != "" {
			query = query.Where("status = ?", user.Status)
		}
		if user.Phonenumber != "" {
			query = query.Where("phonenumber like ?", "%"+user.Phonenumber+"%")
		}
		if user.DeptId > 0 {
			// 查询部门及其子部门
			deptIds := s.getDeptAndChildrenIds(user.DeptId)
			query = query.Where("dept_id in ?", deptIds)
		}
	}

	// 应用数据权限过滤
	query = s.applyDataScope(ctx, query)

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 查询用户列表，预加载部门信息
	err := query.Preload("Dept", "del_flag = '0'").Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// CreateUser 创建用户
func (s *userServiceImpl) CreateUser(ctx context.Context, user *model.SysUser) error {
	// 检查用户名唯一性
	isUnique, err := s.CheckUserNameUnique(ctx, 0, user.UserName)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("用户名已存在")
	}

	// 检查手机号唯一性
	if user.Phonenumber != "" {
		isUnique, err = s.CheckPhoneUnique(ctx, 0, user.Phonenumber)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("手机号已存在")
		}
	}

	// 检查邮箱唯一性
	if user.Email != "" {
		isUnique, err = s.CheckEmailUnique(ctx, 0, user.Email)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("邮箱已存在")
		}
	}

	// 密码加密
	if user.Password != "" {
		user.Password = utils.EncryptPassword(user.Password)
	}

	// 设置创建时间
	user.CreateTime = time.Now()

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 保存用户信息
		if err := tx.Create(user).Error; err != nil {
			return err
		}

		// 保存用户角色关系
		if len(user.RoleIds) > 0 {
			userRoles := make([]model.SysUserRole, 0, len(user.RoleIds))
			for _, roleId := range user.RoleIds {
				userRoles = append(userRoles, model.SysUserRole{
					UserId: user.UserId,
					RoleId: roleId,
				})
			}
			if err := tx.Create(&userRoles).Error; err != nil {
				return err
			}
		}

		// 保存用户岗位关系
		if len(user.PostIds) > 0 {
			userPosts := make([]model.SysUserPost, 0, len(user.PostIds))
			for _, postId := range user.PostIds {
				userPosts = append(userPosts, model.SysUserPost{
					UserId: user.UserId,
					PostId: postId,
				})
			}
			if err := tx.Create(&userPosts).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// UpdateUser 更新用户
func (s *userServiceImpl) UpdateUser(ctx context.Context, user *model.SysUser) error {
	// 检查用户名唯一性
	isUnique, err := s.CheckUserNameUnique(ctx, user.UserId, user.UserName)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("用户名已存在")
	}

	// 检查手机号唯一性
	if user.Phonenumber != "" {
		isUnique, err = s.CheckPhoneUnique(ctx, user.UserId, user.Phonenumber)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("手机号已存在")
		}
	}

	// 检查邮箱唯一性
	if user.Email != "" {
		isUnique, err = s.CheckEmailUnique(ctx, user.UserId, user.Email)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("邮箱已存在")
		}
	}

	// 设置更新时间
	user.UpdateTime = time.Now()

	// 查询原用户信息
	var oldUser model.SysUser
	if err := s.db.Where("user_id = ?", user.UserId).First(&oldUser).Error; err != nil {
		return err
	}

	// 不能修改管理员用户名
	if oldUser.IsAdmin() && oldUser.UserName != user.UserName {
		return errors.New("管理员用户不能修改用户名")
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新用户信息，不更新密码
		updates := map[string]interface{}{
			"nick_name":   user.NickName,
			"email":       user.Email,
			"phonenumber": user.Phonenumber,
			"sex":         user.Sex,
			"status":      user.Status,
			"dept_id":     user.DeptId,
			"update_by":   user.UpdateBy,
			"update_time": user.UpdateTime,
			"remark":      user.Remark,
		}
		if err := tx.Model(&model.SysUser{}).Where("user_id = ?", user.UserId).Updates(updates).Error; err != nil {
			return err
		}

		// 删除原有用户角色关系
		if err := tx.Where("user_id = ?", user.UserId).Delete(&model.SysUserRole{}).Error; err != nil {
			return err
		}

		// 保存用户角色关系
		if len(user.RoleIds) > 0 {
			userRoles := make([]model.SysUserRole, 0, len(user.RoleIds))
			for _, roleId := range user.RoleIds {
				userRoles = append(userRoles, model.SysUserRole{
					UserId: user.UserId,
					RoleId: roleId,
				})
			}
			if err := tx.Create(&userRoles).Error; err != nil {
				return err
			}
		}

		// 删除原有用户岗位关系
		if err := tx.Where("user_id = ?", user.UserId).Delete(&model.SysUserPost{}).Error; err != nil {
			return err
		}

		// 保存用户岗位关系
		if len(user.PostIds) > 0 {
			userPosts := make([]model.SysUserPost, 0, len(user.PostIds))
			for _, postId := range user.PostIds {
				userPosts = append(userPosts, model.SysUserPost{
					UserId: user.UserId,
					PostId: postId,
				})
			}
			if err := tx.Create(&userPosts).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// DeleteUserByIds 批量删除用户
func (s *userServiceImpl) DeleteUserByIds(ctx context.Context, userIds []uint64) error {
	// 检查是否包含管理员
	for _, userId := range userIds {
		if userId == 1 {
			return errors.New("不能删除超级管理员用户")
		}
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 逻辑删除用户
		if err := tx.Model(&model.SysUser{}).Where("user_id in ?", userIds).
			Updates(map[string]interface{}{
				"del_flag":    "2",
				"update_time": time.Now(),
			}).Error; err != nil {
			return err
		}

		// 删除用户角色关系
		if err := tx.Where("user_id in ?", userIds).Delete(&model.SysUserRole{}).Error; err != nil {
			return err
		}

		// 删除用户岗位关系
		if err := tx.Where("user_id in ?", userIds).Delete(&model.SysUserPost{}).Error; err != nil {
			return err
		}

		return nil
	})
}

// ResetUserPassword 重置用户密码
func (s *userServiceImpl) ResetUserPassword(ctx context.Context, userId uint64, password string) error {
	// 加密密码
	encryptedPassword := utils.EncryptPassword(password)

	// 更新密码
	return s.db.Model(&model.SysUser{}).Where("user_id = ?", userId).
		Updates(map[string]interface{}{
			"password":    encryptedPassword,
			"update_time": time.Now(),
		}).Error
}

// UpdateUserStatus 更新用户状态
func (s *userServiceImpl) UpdateUserStatus(ctx context.Context, userId uint64, status string) error {
	// 不能停用管理员
	if userId == 1 && status == "1" {
		return errors.New("不能停用超级管理员用户")
	}

	// 更新状态
	return s.db.Model(&model.SysUser{}).Where("user_id = ?", userId).
		Updates(map[string]interface{}{
			"status":      status,
			"update_time": time.Now(),
		}).Error
}

// GetUserRoles 获取用户角色
func (s *userServiceImpl) GetUserRoles(ctx context.Context, userId uint64) ([]*model.SysRole, error) {
	var roles []*model.SysRole
	err := s.db.Table("sys_role r").
		Joins("left join sys_user_role ur on ur.role_id = r.role_id").
		Where("ur.user_id = ? AND r.del_flag = '0'", userId).
		Find(&roles).Error
	return roles, err
}

// GetUserPosts 获取用户岗位
func (s *userServiceImpl) GetUserPosts(ctx context.Context, userId uint64) ([]*model.SysPost, error) {
	var posts []*model.SysPost
	err := s.db.Table("sys_post p").
		Joins("left join sys_user_post up on up.post_id = p.post_id").
		Where("up.user_id = ?", userId).
		Find(&posts).Error
	return posts, err
}

// AssignUserRoles 分配用户角色
func (s *userServiceImpl) AssignUserRoles(ctx context.Context, userId uint64, roleIds []uint64) error {
	// 不能修改管理员角色
	if userId == 1 {
		return errors.New("不能修改超级管理员用户的角色")
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除原有用户角色关系
		if err := tx.Where("user_id = ?", userId).Delete(&model.SysUserRole{}).Error; err != nil {
			return err
		}

		// 保存用户角色关系
		if len(roleIds) > 0 {
			userRoles := make([]model.SysUserRole, 0, len(roleIds))
			for _, roleId := range roleIds {
				userRoles = append(userRoles, model.SysUserRole{
					UserId: userId,
					RoleId: roleId,
				})
			}
			if err := tx.Create(&userRoles).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// UpdateUserProfile 更新用户个人信息
func (s *userServiceImpl) UpdateUserProfile(ctx context.Context, user *model.SysUser) error {
	// 检查手机号唯一性
	if user.Phonenumber != "" {
		isUnique, err := s.CheckPhoneUnique(ctx, user.UserId, user.Phonenumber)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("手机号已存在")
		}
	}

	// 检查邮箱唯一性
	if user.Email != "" {
		isUnique, err := s.CheckEmailUnique(ctx, user.UserId, user.Email)
		if err != nil {
			return err
		}
		if !isUnique {
			return errors.New("邮箱已存在")
		}
	}

	// 设置更新时间
	user.UpdateTime = time.Now()

	// 更新用户信息
	return s.db.Model(&model.SysUser{}).Where("user_id = ?", user.UserId).
		Updates(map[string]interface{}{
			"nick_name":   user.NickName,
			"email":       user.Email,
			"phonenumber": user.Phonenumber,
			"sex":         user.Sex,
			"update_time": user.UpdateTime,
		}).Error
}

// UpdateUserPassword 修改用户密码
func (s *userServiceImpl) UpdateUserPassword(ctx context.Context, userId uint64, oldPassword, newPassword string) error {
	// 查询用户
	var user model.SysUser
	if err := s.db.Where("user_id = ?", userId).First(&user).Error; err != nil {
		return err
	}

	// 验证旧密码
	if !utils.MatchPassword(oldPassword, user.Password) {
		return errors.New("旧密码错误")
	}

	// 加密新密码
	encryptedPassword := utils.EncryptPassword(newPassword)

	// 更新密码
	return s.db.Model(&model.SysUser{}).Where("user_id = ?", userId).
		Updates(map[string]interface{}{
			"password":    encryptedPassword,
			"update_time": time.Now(),
		}).Error
}

// UpdateUserAvatar 更新用户头像
func (s *userServiceImpl) UpdateUserAvatar(ctx context.Context, userId uint64, avatar string) error {
	// 更新头像
	return s.db.Model(&model.SysUser{}).Where("user_id = ?", userId).
		Updates(map[string]interface{}{
			"avatar":      avatar,
			"update_time": time.Now(),
		}).Error
}

// CheckUserNameUnique 检查用户名是否唯一
func (s *userServiceImpl) CheckUserNameUnique(ctx context.Context, userId uint64, userName string) (bool, error) {
	var count int64
	query := s.db.Model(&model.SysUser{}).Where("user_name = ? AND del_flag = '0'", userName)
	if userId > 0 {
		query = query.Where("user_id != ?", userId)
	}
	err := query.Count(&count).Error
	return count == 0, err
}

// CheckPhoneUnique 检查手机号是否唯一
func (s *userServiceImpl) CheckPhoneUnique(ctx context.Context, userId uint64, phone string) (bool, error) {
	var count int64
	query := s.db.Model(&model.SysUser{}).Where("phonenumber = ? AND del_flag = '0'", phone)
	if userId > 0 {
		query = query.Where("user_id != ?", userId)
	}
	err := query.Count(&count).Error
	return count == 0, err
}

// CheckEmailUnique 检查邮箱是否唯一
func (s *userServiceImpl) CheckEmailUnique(ctx context.Context, userId uint64, email string) (bool, error) {
	var count int64
	query := s.db.Model(&model.SysUser{}).Where("email = ? AND del_flag = '0'", email)
	if userId > 0 {
		query = query.Where("user_id != ?", userId)
	}
	err := query.Count(&count).Error
	return count == 0, err
}

// 获取部门及其子部门ID
func (s *userServiceImpl) getDeptAndChildrenIds(deptId uint64) []uint64 {
	var depts []model.SysDept
	s.db.Where("del_flag = '0'").Find(&depts)

	deptMap := make(map[uint64]*model.SysDept)
	for i := range depts {
		deptMap[depts[i].DeptId] = &depts[i]
	}

	deptIds := []uint64{deptId}
	childDeptIds := s.getChildDeptIds(deptMap, deptId)
	deptIds = append(deptIds, childDeptIds...)

	return deptIds
}

// 递归获取子部门ID
func (s *userServiceImpl) getChildDeptIds(deptMap map[uint64]*model.SysDept, deptId uint64) []uint64 {
	deptIds := make([]uint64, 0)

	for id, dept := range deptMap {
		if dept.ParentId == deptId {
			deptIds = append(deptIds, id)
			childIds := s.getChildDeptIds(deptMap, id)
			deptIds = append(deptIds, childIds...)
		}
	}

	return deptIds
}

// 应用数据权限过滤
func (s *userServiceImpl) applyDataScope(ctx context.Context, query *gorm.DB) *gorm.DB {
	// 获取当前用户
	// 假设当前用户信息从上下文获取
	// currentUser := getCurrentUserFromContext(ctx)

	// TODO: 实现数据权限过滤
	// 这里是一个简化实现，实际情况下需要根据用户角色的数据权限进行过滤

	// 示例：
	// 1. 如果是管理员，不进行过滤
	// 2. 如果是自定义数据权限，根据角色和部门关联过滤
	// 3. 如果是本部门数据权限，只能查看本部门数据
	// 4. 如果是本部门及以下数据权限，可以查看本部门及子部门数据
	// 5. 如果是仅本人数据权限，只能查看自己创建的数据

	return query
}
