package model

import (
	"time"
)

// SysUserOnline 在线用户记录 sys_user_online
type SysUserOnline struct {
	SessionId      string    `json:"tokenId" gorm:"column:sessionId;type:varchar(50);primaryKey;comment:用户会话id"`
	LoginName      string    `json:"loginName" gorm:"column:login_name;type:varchar(50);comment:登录账号"`
	UserName       string    `json:"userName" gorm:"-;comment:用户名称"`
	DeptName       string    `json:"deptName" gorm:"column:dept_name;type:varchar(50);comment:部门名称"`
	Ipaddr         string    `json:"ipaddr" gorm:"column:ipaddr;type:varchar(50);comment:登录IP地址"`
	LoginLocation  string    `json:"loginLocation" gorm:"column:login_location;type:varchar(255);comment:登录地点"`
	Browser        string    `json:"browser" gorm:"column:browser;type:varchar(50);comment:浏览器类型"`
	Os             string    `json:"os" gorm:"column:os;type:varchar(50);comment:操作系统"`
	Status         string    `json:"status" gorm:"column:status;type:varchar(10);comment:在线状态on_line在线off_line离线"`
	StartTimestamp time.Time `json:"startTimestamp" gorm:"column:start_timestamp;comment:session创建时间"`
	LastAccessTime time.Time `json:"lastAccessTime" gorm:"column:last_access_time;comment:session最后访问时间"`
	ExpireTime     int       `json:"expireTime" gorm:"column:expire_time;comment:超时时间，单位为分钟"`
	LoginTime      int64     `json:"loginTime" gorm:"-;comment:登录时间戳"`
}

// TableName 设置表名
func (SysUserOnline) TableName() string {
	return "sys_user_online"
}
