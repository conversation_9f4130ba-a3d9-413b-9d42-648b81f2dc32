package model

// SysConfig 参数配置表 sys_config
type SysConfig struct {
	BaseModel
	ConfigId    uint64 `json:"configId" gorm:"column:config_id;primaryKey;comment:参数主键"`
	ConfigName  string `json:"configName" gorm:"column:config_name;type:varchar(100);comment:参数名称"`
	ConfigKey   string `json:"configKey" gorm:"column:config_key;type:varchar(100);comment:参数键名"`
	ConfigValue string `json:"configValue" gorm:"column:config_value;type:varchar(500);comment:参数键值"`
	ConfigType  string `json:"configType" gorm:"column:config_type;type:char(1);comment:系统内置（Y是 N否）"`
}

// TableName 设置表名
func (SysConfig) TableName() string {
	return "sys_config"
}
