package logger

import (
	"os"
	"path/filepath"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	// Logger 全局日志对象
	Logger *zap.Logger
	// Default 默认日志核心
	Default zapcore.Core
)

// Setup 初始化日志
func Setup() {
	// 创建日志目录
	logFilename := viper.GetString("log.filename")
	logDir := filepath.Dir(logFilename)
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		if err := os.MkdirAll(logDir, 0755); err != nil {
			panic("创建日志目录失败: " + err.Error())
		}
	}

	// 设置日志级别
	var level zapcore.Level
	switch viper.GetString("log.level") {
	case "debug":
		level = zap.DebugLevel
	case "info":
		level = zap.InfoLevel
	case "warn":
		level = zap.WarnLevel
	case "error":
		level = zap.ErrorLevel
	case "dpanic":
		level = zap.DPanicLevel
	case "panic":
		level = zap.PanicLevel
	case "fatal":
		level = zap.FatalLevel
	default:
		level = zap.InfoLevel
	}

	// 日志切割配置
	hook := lumberjack.Logger{
		Filename:   logFilename,                     // 日志文件路径
		MaxSize:    viper.GetInt("log.max_size"),    // 单个日志文件最大尺寸，单位 MB
		MaxBackups: viper.GetInt("log.max_backups"), // 保留的旧日志文件最大数量
		MaxAge:     viper.GetInt("log.max_age"),     // 保留的旧日志文件最大天数
		Compress:   viper.GetBool("log.compress"),   // 是否压缩旧日志文件
	}

	// 编码器配置
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// 创建核心
	Default = zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig), // 编码器
		zapcore.NewMultiWriteSyncer( // 输出
			zapcore.AddSync(os.Stdout), // 输出到控制台
			zapcore.AddSync(&hook),     // 输出到文件
		),
		zap.NewAtomicLevelAt(level), // 日志级别
	)

	// 创建Logger
	Logger = zap.New(
		Default,
		zap.AddCaller(),                       // 调用者信息
		zap.AddCallerSkip(1),                  // 跳过封装函数
		zap.AddStacktrace(zapcore.ErrorLevel), // 错误时打印堆栈
	)

	// 替换全局Logger
	zap.ReplaceGlobals(Logger)
}

// Debug 调试日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal 致命错误日志，输出后会调用os.Exit(1)
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}

// LogLevel 日志级别类型，用于GORM日志
type LogLevel = zapcore.Level

// 日志级别常量
const (
	DebugLevel  = zapcore.DebugLevel
	InfoLevel   = zapcore.InfoLevel
	WarnLevel   = zapcore.WarnLevel
	ErrorLevel  = zapcore.ErrorLevel
	DPanicLevel = zapcore.DPanicLevel
	PanicLevel  = zapcore.PanicLevel
	FatalLevel  = zapcore.FatalLevel
)
