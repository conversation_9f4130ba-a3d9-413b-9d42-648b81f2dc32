# 菜单管理API接口分析

## 模块概述

菜单管理模块是系统界面构建和权限控制的核心，主要负责系统菜单、目录和按钮的增删改查，以及前端路由和权限标识的管理。

## API接口列表

### 1. 菜单列表查询

- **接口路径**：`/system/menu/list`
- **请求方法**：GET
- **接口说明**：查询菜单列表，支持多条件筛选
- **权限标识**：`system:menu:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuName | string | 否 | 菜单名称 |
| status | string | 否 | 状态（0正常 1停用） |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "menuId": 1,
      "menuName": "系统管理",
      "parentId": 0,
      "orderNum": 1,
      "path": "system",
      "component": null,
      "isFrame": 1,
      "isCache": 0,
      "menuType": "M",
      "visible": "0",
      "status": "0",
      "perms": null,
      "icon": "system",
      "createTime": "2023-01-01 12:00:00",
      "children": [
        {
          "menuId": 100,
          "menuName": "用户管理",
          "parentId": 1,
          "orderNum": 1,
          "path": "user",
          "component": "system/user/index",
          "isFrame": 1,
          "isCache": 0,
          "menuType": "C",
          "visible": "0",
          "status": "0",
          "perms": "system:user:list",
          "icon": "user",
          "createTime": "2023-01-01 12:00:00",
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 获取菜单详情

- **接口路径**：`/system/menu/{menuId}`
- **请求方法**：GET
- **接口说明**：根据菜单ID查询菜单详细信息
- **权限标识**：`system:menu:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuId | integer | 是 | 菜单ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "menuId": 100,
    "menuName": "用户管理",
    "parentId": 1,
    "orderNum": 1,
    "path": "user",
    "component": "system/user/index",
    "isFrame": 1,
    "isCache": 0,
    "menuType": "C",
    "visible": "0",
    "status": "0",
    "perms": "system:user:list",
    "icon": "user",
    "createTime": "2023-01-01 12:00:00"
  }
}
```

### 3. 获取菜单下拉树列表

- **接口路径**：`/system/menu/treeselect`
- **请求方法**：GET
- **接口说明**：获取菜单下拉树列表
- **权限标识**：`system:menu:query`
- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "label": "系统管理",
      "children": [
        {
          "id": 100,
          "label": "用户管理",
          "children": []
        }
      ]
    }
  ]
}
```

### 4. 获取角色菜单树列表

- **接口路径**：`/system/menu/roleMenuTreeselect/{roleId}`
- **请求方法**：GET
- **接口说明**：获取角色菜单树列表
- **权限标识**：`system:menu:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "checkedKeys": [1, 100],
    "menus": [
      {
        "id": 1,
        "label": "系统管理",
        "children": [
          {
            "id": 100,
            "label": "用户管理",
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 5. 新增菜单

- **接口路径**：`/system/menu`
- **请求方法**：POST
- **接口说明**：新增菜单信息
- **权限标识**：`system:menu:add`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuName | string | 是 | 菜单名称 |
| parentId | integer | 是 | 父菜单ID |
| orderNum | integer | 是 | 显示顺序 |
| path | string | 否 | 路由地址 |
| component | string | 否 | 组件路径 |
| query | string | 否 | 路由参数 |
| isFrame | integer | 否 | 是否为外链（0是 1否） |
| isCache | integer | 否 | 是否缓存（0缓存 1不缓存） |
| menuType | string | 是 | 菜单类型（M目录 C菜单 F按钮） |
| visible | string | 否 | 菜单状态（0显示 1隐藏） |
| status | string | 否 | 菜单状态（0正常 1停用） |
| perms | string | 否 | 权限标识 |
| icon | string | 否 | 菜单图标 |
| remark | string | 否 | 备注 |

- **请求示例**：
```json
{
  "menuName": "部门管理",
  "parentId": 1,
  "orderNum": 2,
  "path": "dept",
  "component": "system/dept/index",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:dept:list",
  "icon": "tree",
  "remark": "部门管理菜单"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

### 6. 修改菜单

- **接口路径**：`/system/menu`
- **请求方法**：PUT
- **接口说明**：修改菜单信息
- **权限标识**：`system:menu:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuId | integer | 是 | 菜单ID |
| menuName | string | 是 | 菜单名称 |
| parentId | integer | 是 | 父菜单ID |
| orderNum | integer | 是 | 显示顺序 |
| path | string | 否 | 路由地址 |
| component | string | 否 | 组件路径 |
| query | string | 否 | 路由参数 |
| isFrame | integer | 否 | 是否为外链（0是 1否） |
| isCache | integer | 否 | 是否缓存（0缓存 1不缓存） |
| menuType | string | 是 | 菜单类型（M目录 C菜单 F按钮） |
| visible | string | 否 | 菜单状态（0显示 1隐藏） |
| status | string | 否 | 菜单状态（0正常 1停用） |
| perms | string | 否 | 权限标识 |
| icon | string | 否 | 菜单图标 |
| remark | string | 否 | 备注 |

- **请求示例**：
```json
{
  "menuId": 101,
  "menuName": "部门管理修改",
  "parentId": 1,
  "orderNum": 3,
  "path": "dept",
  "component": "system/dept/index",
  "isFrame": 1,
  "isCache": 0,
  "menuType": "C",
  "visible": "0",
  "status": "0",
  "perms": "system:dept:list",
  "icon": "tree-table",
  "remark": "部门管理菜单已修改"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 7. 删除菜单

- **接口路径**：`/system/menu/{menuId}`
- **请求方法**：DELETE
- **接口说明**：删除菜单信息
- **权限标识**：`system:menu:remove`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| menuId | integer | 是 | 菜单ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

## 接口实现注意事项

1. **菜单层级关系**：
   - 菜单是树形结构，需要处理好父子关系
   - 查询列表时通常需要返回树形结构

2. **菜单类型约束**：
   - 目录（M）：component通常为空，path必填
   - 菜单（C）：component和path必填，perms通常必填
   - 按钮（F）：perms必填，path和component通常为空

3. **特殊菜单保护**：
   - 不能删除有子菜单的菜单节点
   - 不能删除已分配给角色的菜单

4. **父菜单更新**：
   - 修改菜单的父ID时，需要确保不会产生循环引用
   - 需要验证父菜单类型（目录下可以挂载目录和菜单，菜单下只能挂载按钮）

5. **前端路由生成**：
   - 系统需要根据菜单数据生成Vue路由配置
   - 只有类型为"M"和"C"且状态为正常的菜单才会生成路由

6. **权限控制**：
   - 权限标识（perms）用于后端接口权限控制
   - 按钮权限（menuType=F）用于前端按钮显示控制

## Gin+GORM实现建议

1. **Controller层**：
```go
// MenuController 处理菜单相关请求
type MenuController struct {
    MenuService service.IMenuService
}

// List 获取菜单列表
func (c *MenuController) List(ctx *gin.Context) {
    // 解析查询参数
    menuName := ctx.Query("menuName")
    status := ctx.Query("status")
    
    // 调用Service层获取数据
    menus, err := c.MenuService.SelectMenuList(ctx, menuName, status)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    // 返回结果
    response.Success(ctx, menus)
}

// GetInfo 获取菜单详情
func (c *MenuController) GetInfo(ctx *gin.Context) {
    menuId, err := strconv.ParseUint(ctx.Param("menuId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    menu, err := c.MenuService.SelectMenuById(ctx, menuId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    response.Success(ctx, menu)
}

// TreeSelect 获取菜单下拉树列表
func (c *MenuController) TreeSelect(ctx *gin.Context) {
    // 调用Service层获取菜单树
    menus, err := c.MenuService.SelectMenuTreeAll(ctx)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    response.Success(ctx, menus)
}

// RoleMenuTreeSelect 获取角色菜单树列表
func (c *MenuController) RoleMenuTreeSelect(ctx *gin.Context) {
    roleId, err := strconv.ParseUint(ctx.Param("roleId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 获取所有菜单树
    menus, err := c.MenuService.SelectMenuTreeAll(ctx)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询菜单树失败")
        return
    }
    
    // 获取角色已选中的菜单ID
    checkedKeys, err := c.MenuService.SelectMenuListByRoleId(ctx, roleId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询角色菜单失败")
        return
    }
    
    response.Success(ctx, gin.H{
        "menus":       menus,
        "checkedKeys": checkedKeys,
    })
}

// Add 添加菜单
func (c *MenuController) Add(ctx *gin.Context) {
    var req request.MenuAddRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 验证菜单类型的必填字段
    if err := c.validateMenuFields(req); err != nil {
        response.Error(ctx, http.StatusBadRequest, err.Error())
        return
    }
    
    // 创建菜单
    err := c.MenuService.InsertMenu(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "新增菜单失败")
        return
    }
    
    response.Success(ctx, nil)
}

// 其他方法...

// validateMenuFields 验证菜单字段
func (c *MenuController) validateMenuFields(req request.MenuAddRequest) error {
    switch req.MenuType {
    case "M": // 目录
        if req.Path == "" {
            return errors.New("路由地址不能为空")
        }
    case "C": // 菜单
        if req.Path == "" {
            return errors.New("路由地址不能为空")
        }
        if req.Component == "" {
            return errors.New("组件路径不能为空")
        }
    case "F": // 按钮
        if req.Perms == "" {
            return errors.New("权限标识不能为空")
        }
    default:
        return errors.New("菜单类型不正确")
    }
    return nil
}
```

2. **Service层**：
```go
// IMenuService 菜单服务接口
type IMenuService interface {
    SelectMenuList(ctx *gin.Context, menuName, status string) ([]*models.SysMenu, error)
    SelectMenuById(ctx *gin.Context, menuId uint64) (*models.SysMenu, error)
    InsertMenu(ctx *gin.Context, req request.MenuAddRequest) error
    UpdateMenu(ctx *gin.Context, req request.MenuUpdateRequest) error
    DeleteMenuById(ctx *gin.Context, menuId uint64) error
    SelectMenuTreeAll(ctx *gin.Context) ([]*models.TreeSelect, error)
    SelectMenuListByRoleId(ctx *gin.Context, roleId uint64) ([]uint64, error)
    BuildMenuTree(menus []*models.SysMenu) []*models.SysMenu
    BuildTreeSelect(menus []*models.SysMenu) []*models.TreeSelect
    // 其他方法...
}

// MenuService 菜单服务实现
type MenuService struct {
    DB *gorm.DB
}

// SelectMenuList 查询菜单列表
func (s *MenuService) SelectMenuList(ctx *gin.Context, menuName, status string) ([]*models.SysMenu, error) {
    var menus []*models.SysMenu
    
    db := s.DB.Model(&models.SysMenu{})
    
    // 添加查询条件
    if menuName != "" {
        db = db.Where("menu_name like ?", "%"+menuName+"%")
    }
    
    if status != "" {
        db = db.Where("status = ?", status)
    }
    
    // 排序查询
    if err := db.Order("parent_id, order_num").Find(&menus).Error; err != nil {
        return nil, err
    }
    
    return menus, nil
}

// BuildMenuTree 构建菜单树
func (s *MenuService) BuildMenuTree(menus []*models.SysMenu) []*models.SysMenu {
    // 创建根节点
    var roots []*models.SysMenu
    // 创建节点映射，便于查找父节点
    menuMap := make(map[uint64]*models.SysMenu)
    
    // 将所有节点存入map
    for _, menu := range menus {
        // 为每个节点创建空的子节点切片
        menu.Children = []*models.SysMenu{}
        menuMap[menu.MenuId] = menu
    }
    
    // 构建树形结构
    for _, menu := range menus {
        if menu.ParentId == 0 {
            // 如果是根节点，直接添加到roots
            roots = append(roots, menu)
        } else {
            // 如果有父节点，添加到父节点的children
            if parent, exists := menuMap[menu.ParentId]; exists {
                parent.Children = append(parent.Children, menu)
            }
        }
    }
    
    return roots
}

// BuildTreeSelect 构建下拉树选择菜单
func (s *MenuService) BuildTreeSelect(menus []*models.SysMenu) []*models.TreeSelect {
    // 先构建菜单树
    menuTree := s.BuildMenuTree(menus)
    
    // 转换为TreeSelect结构
    return s.convertMenuTreeToTreeSelect(menuTree)
}

// convertMenuTreeToTreeSelect 将菜单树转换为下拉树结构
func (s *MenuService) convertMenuTreeToTreeSelect(menus []*models.SysMenu) []*models.TreeSelect {
    treeSelects := make([]*models.TreeSelect, 0, len(menus))
    
    for _, menu := range menus {
        treeSelect := &models.TreeSelect{
            Id:    menu.MenuId,
            Label: menu.MenuName,
        }
        
        // 递归处理子菜单
        if len(menu.Children) > 0 {
            treeSelect.Children = s.convertMenuTreeToTreeSelect(menu.Children)
        }
        
        treeSelects = append(treeSelects, treeSelect)
    }
    
    return treeSelects
}

// 其他方法...
```

3. **Model层**：
```go
// TreeSelect 下拉树结构
type TreeSelect struct {
    Id       uint64        `json:"id"`
    Label    string        `json:"label"`
    Children []*TreeSelect `json:"children"`
}
```

4. **请求和响应结构**：
```go
// MenuAddRequest 新增菜单请求
type MenuAddRequest struct {
    MenuName   string `json:"menuName" binding:"required"`
    ParentId   uint64 `json:"parentId"`
    OrderNum   int    `json:"orderNum" binding:"required"`
    Path       string `json:"path"`
    Component  string `json:"component"`
    Query      string `json:"query"`
    IsFrame    int    `json:"isFrame"`
    IsCache    int    `json:"isCache"`
    MenuType   string `json:"menuType" binding:"required"`
    Visible    string `json:"visible"`
    Status     string `json:"status"`
    Perms      string `json:"perms"`
    Icon       string `json:"icon"`
    Remark     string `json:"remark"`
}

// MenuUpdateRequest 修改菜单请求
type MenuUpdateRequest struct {
    MenuId     uint64 `json:"menuId" binding:"required"`
    MenuName   string `json:"menuName" binding:"required"`
    ParentId   uint64 `json:"parentId"`
    OrderNum   int    `json:"orderNum" binding:"required"`
    Path       string `json:"path"`
    Component  string `json:"component"`
    Query      string `json:"query"`
    IsFrame    int    `json:"isFrame"`
    IsCache    int    `json:"isCache"`
    MenuType   string `json:"menuType" binding:"required"`
    Visible    string `json:"visible"`
    Status     string `json:"status"`
    Perms      string `json:"perms"`
    Icon       string `json:"icon"`
    Remark     string `json:"remark"`
}
```

5. **路由注册**：
```go
// 注册菜单管理路由
func RegisterMenuRoutes(r *gin.RouterGroup, menuController *controller.MenuController) {
    menuRouter := r.Group("/system/menu")
    {
        menuRouter.GET("/list", middleware.Auth(), middleware.HasPermission("system:menu:list"), menuController.List)
        menuRouter.GET("/:menuId", middleware.Auth(), middleware.HasPermission("system:menu:query"), menuController.GetInfo)
        menuRouter.GET("/treeselect", middleware.Auth(), middleware.HasPermission("system:menu:query"), menuController.TreeSelect)
        menuRouter.GET("/roleMenuTreeselect/:roleId", middleware.Auth(), middleware.HasPermission("system:menu:query"), menuController.RoleMenuTreeSelect)
        menuRouter.POST("", middleware.Auth(), middleware.HasPermission("system:menu:add"), menuController.Add)
        menuRouter.PUT("", middleware.Auth(), middleware.HasPermission("system:menu:edit"), menuController.Update)
        menuRouter.DELETE("/:menuId", middleware.Auth(), middleware.HasPermission("system:menu:remove"), menuController.Delete)
    }
} 