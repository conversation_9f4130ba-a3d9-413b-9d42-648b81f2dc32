package model

// SysDictData 字典数据表 sys_dict_data
type SysDictData struct {
	BaseModel
	DictCode  uint64 `json:"dictCode" gorm:"column:dict_code;primaryKey;comment:字典编码"`
	DictSort  int64  `json:"dictSort" gorm:"column:dict_sort;default:0;comment:字典排序"`
	DictLabel string `json:"dictLabel" gorm:"column:dict_label;type:varchar(100);comment:字典标签"`
	DictValue string `json:"dictValue" gorm:"column:dict_value;type:varchar(100);comment:字典键值"`
	DictType  string `json:"dictType" gorm:"column:dict_type;type:varchar(100);comment:字典类型"`
	CssClass  string `json:"cssClass" gorm:"column:css_class;type:varchar(100);comment:样式属性（其他样式扩展）"`
	ListClass string `json:"listClass" gorm:"column:list_class;type:varchar(100);comment:表格回显样式"`
	IsDefault string `json:"isDefault" gorm:"column:is_default;type:char(1);default:N;comment:是否默认（Y是 N否）"`
	Status    string `json:"status" gorm:"column:status;type:char(1);default:0;comment:状态（0正常 1停用）"`
}

// TableName 设置表名
func (SysDictData) TableName() string {
	return "sys_dict_data"
}

// IsDefaultY 判断是否是默认值
func (d *SysDictData) IsDefaultY() bool {
	return d.IsDefault == "Y"
}
