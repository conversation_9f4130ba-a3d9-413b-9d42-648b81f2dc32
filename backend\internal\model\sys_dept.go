package model

// SysDept 部门表 sys_dept
type SysDept struct {
	BaseEntity
	DeptId    uint64 `json:"deptId" gorm:"column:dept_id;primaryKey;comment:部门id"`
	ParentId  uint64 `json:"parentId" gorm:"column:parent_id;default:0;comment:父部门id"`
	Ancestors string `json:"ancestors" gorm:"column:ancestors;type:varchar(50);default:'';comment:祖级列表"`
	DeptName  string `json:"deptName" gorm:"column:dept_name;type:varchar(30);default:'';comment:部门名称"`
	OrderNum  int    `json:"orderNum" gorm:"column:order_num;default:0;comment:显示顺序"`
	Leader    string `json:"leader" gorm:"column:leader;type:varchar(20);comment:负责人"`
	Phone     string `json:"phone" gorm:"column:phone;type:varchar(11);comment:联系电话"`
	Email     string `json:"email" gorm:"column:email;type:varchar(50);comment:邮箱"`
	Status    string `json:"status" gorm:"column:status;type:char(1);default:0;comment:部门状态（0正常 1停用）"`

	// 非数据库字段
	ParentName string     `json:"parentName" gorm:"-"` // 父部门名称
	Children   []*SysDept `json:"children" gorm:"-"`   // 子部门
}

// TableName 设置表名
func (SysDept) TableName() string {
	return "sys_dept"
}
