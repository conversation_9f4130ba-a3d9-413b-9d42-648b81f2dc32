package utils

import (
	"github.com/ruoyi-go/internal/model"
)

// BuildDeptTreeSelect 构建部门下拉树结构
func BuildDeptTreeSelect(depts []*model.SysDept) []*model.TreeSelect {
	// 先构建部门树
	deptTree := BuildDeptTree(depts)

	// 转换为下拉树结构
	treeSelects := make([]*model.TreeSelect, 0, len(deptTree))
	for _, dept := range deptTree {
		treeSelects = append(treeSelects, NewDeptTreeSelect(dept))
	}

	return treeSelects
}

// BuildMenuTreeSelect 构建菜单下拉树结构
func BuildMenuTreeSelect(menus []*model.SysMenu) []*model.TreeSelect {
	// 先构建菜单树
	menuTree := BuildMenuTree(menus)

	// 转换为下拉树结构
	treeSelects := make([]*model.TreeSelect, 0, len(menuTree))
	for _, menu := range menuTree {
		treeSelects = append(treeSelects, NewMenuTreeSelect(menu))
	}

	return treeSelects
}

// BuildDeptTree 构建部门树
func BuildDeptTree(depts []*model.SysDept) []*model.SysDept {
	// 创建根节点
	var roots []*model.SysDept
	// 创建节点映射，便于查找父节点
	deptMap := make(map[uint64]*model.SysDept)

	// 将所有节点存入map
	for _, dept := range depts {
		// 为每个节点创建空的子节点切片
		dept.Children = []*model.SysDept{}
		deptMap[dept.DeptId] = dept
	}

	// 构建树形结构
	for _, dept := range depts {
		if dept.ParentId == 0 {
			// 如果是根节点，直接添加到roots
			roots = append(roots, dept)
		} else {
			// 如果有父节点，添加到父节点的children
			if parent, exists := deptMap[dept.ParentId]; exists {
				parent.Children = append(parent.Children, dept)
			}
		}
	}

	return roots
}

// BuildMenuTree 构建菜单树
func BuildMenuTree(menus []*model.SysMenu) []*model.SysMenu {
	// 创建根节点
	var roots []*model.SysMenu
	// 创建节点映射，便于查找父节点
	menuMap := make(map[uint64]*model.SysMenu)

	// 将所有节点存入map
	for _, menu := range menus {
		// 为每个节点创建空的子节点切片
		menu.Children = []*model.SysMenu{}
		menuMap[menu.MenuId] = menu
	}

	// 构建树形结构
	for _, menu := range menus {
		if menu.ParentId == 0 {
			// 如果是根节点，直接添加到roots
			roots = append(roots, menu)
		} else {
			// 如果有父节点，添加到父节点的children
			if parent, exists := menuMap[menu.ParentId]; exists {
				parent.Children = append(parent.Children, menu)
			}
		}
	}

	return roots
}

// NewDeptTreeSelect 根据部门创建树选择节点
func NewDeptTreeSelect(dept *model.SysDept) *model.TreeSelect {
	tree := &model.TreeSelect{
		Id:       dept.DeptId,
		Label:    dept.DeptName,
		Disabled: dept.Status == "1", // 1代表停用
	}

	// 递归处理子部门
	if len(dept.Children) > 0 {
		tree.Children = make([]*model.TreeSelect, 0, len(dept.Children))
		for _, child := range dept.Children {
			tree.Children = append(tree.Children, NewDeptTreeSelect(child))
		}
	}

	return tree
}

// NewMenuTreeSelect 根据菜单创建树选择节点
func NewMenuTreeSelect(menu *model.SysMenu) *model.TreeSelect {
	tree := &model.TreeSelect{
		Id:       menu.MenuId,
		Label:    menu.MenuName,
		Disabled: menu.Status == "1", // 1代表停用
	}

	// 递归处理子菜单
	if len(menu.Children) > 0 {
		tree.Children = make([]*model.TreeSelect, 0, len(menu.Children))
		for _, child := range menu.Children {
			tree.Children = append(tree.Children, NewMenuTreeSelect(child))
		}
	}

	return tree
}
