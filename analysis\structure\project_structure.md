# RuoYi项目结构分析

## 项目概述

RuoYi是一个基于SpringBoot+Vue的前后端分离的Java快速开发框架，版本为v3.9.0。系统包含18个内置功能模块，采用了主流的技术栈。

## 模块结构

项目采用Maven多模块结构，主要包含以下模块：

### 1. ruoyi-admin

系统入口模块，包含启动类、配置文件和控制器。

- **主要功能**：系统启动、配置加载、请求处理
- **关键类**：
  - `RuoYiApplication`: 系统启动类
  - `SysLoginController`: 登录控制器
  - `SysUserController`: 用户管理控制器

### 2. ruoyi-framework

框架核心模块，包含安全配置、异常处理、拦截器等。

- **主要功能**：安全框架、权限控制、数据源配置
- **关键组件**：
  - 安全认证
  - 权限拦截
  - 数据源管理

### 3. ruoyi-system

系统核心业务模块，包含主要业务逻辑和数据访问层。

- **主要功能**：用户管理、角色管理、部门管理等核心业务
- **关键包**：
  - `domain`: 实体类
  - `mapper`: 数据访问层
  - `service`: 业务逻辑层

### 4. ruoyi-quartz

任务调度模块，负责定时任务的管理和执行。

- **主要功能**：任务创建、执行、日志记录
- **关键组件**：
  - 任务调度器
  - 任务执行器
  - 任务日志记录

### 5. ruoyi-generator

代码生成模块，用于根据数据库表自动生成代码。

- **主要功能**：生成实体类、Mapper、Service、Controller等代码
- **关键组件**：
  - 模板引擎
  - 代码生成器
  - 表结构分析

### 6. ruoyi-common

通用工具模块，包含各种工具类和通用功能。

- **主要功能**：工具类、通用注解、常量定义
- **关键包**：
  - `utils`: 工具类
  - `constant`: 常量
  - `annotation`: 注解

## 前端结构

前端采用Vue + Element UI构建，主要目录结构：

### 1. src/api

API接口定义，按模块组织。

### 2. src/views

页面视图组件，对应后端各功能模块。

### 3. src/router

路由配置，定义页面跳转规则。

### 4. src/store

Vuex状态管理，存储全局状态。

### 5. src/utils

工具函数，包含请求封装、权限控制等。

### 6. src/components

通用组件，如表格、表单、上传等。

## 数据库结构

系统默认使用MySQL数据库，主要表结构：

1. **用户相关**：sys_user, sys_role, sys_menu, sys_dept
2. **权限相关**：sys_role_menu, sys_user_role, sys_role_dept
3. **配置相关**：sys_config, sys_dict_type, sys_dict_data
4. **日志相关**：sys_logininfor, sys_oper_log
5. **其他**：sys_job, sys_notice

## 关键技术点

1. **权限控制**：基于RBAC模型，使用Spring Security实现
2. **数据访问**：使用MyBatis作为ORM框架
3. **缓存处理**：使用Redis实现缓存
4. **认证机制**：基于JWT的无状态认证
5. **任务调度**：基于Quartz的定时任务
6. **代码生成**：基于Velocity模板引擎的代码生成

## 迁移重点关注

1. **权限模型转换**：从Spring Security到自定义权限系统
2. **ORM映射**：从MyBatis到GORM
3. **缓存处理**：Redis使用方式调整
4. **认证机制**：JWT实现方式转换
5. **事务处理**：事务管理方式转换
6. **代码生成**：重新实现代码生成功能 