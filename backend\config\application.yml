# 应用配置
app:
  # 名称
  name: RuoYi-Go
  # 版本
  version: 1.0.0
  # 版权年份
  copyright_year: 2023
  # 文件路径
  profile: D:/ruoyi-go/uploadPath
  # 获取IP地址开关
  address_enabled: false
  # 验证码类型 math:数学运算 char:字符验证
  captcha_type: math

# 服务器配置
server:
  # 服务器地址
  host: 0.0.0.0
  # 服务器端口
  port: 8080
  # 应用的访问路径
  context_path: /
  # 运行模式 debug:调试模式 release:生产模式
  mode: debug

# 数据库配置
database:
  # 数据库类型
  driver: sqlserver
  # 服务器地址
  host: localhost
  # 服务器端口
  port: 1433
  # 数据库名
  database: wosm
  # 用户名
  username: sa
  # 密码
  password: F@2233
  # 字符集
  charset: utf8
  # 连接池最大闲置连接数
  max_idle_conns: 10
  # 连接池最大连接数
  max_open_conns: 100
  # 连接的最大生存时间(分钟)
  conn_max_lifetime: 60
  # 是否显示SQL
  show_sql: true

# Redis配置
redis:
  # 地址
  host: localhost
  # 端口
  port: 6379
  # 密码
  password: 
  # 数据库索引
  database: 0
  # 连接超时时间(秒)
  timeout: 10

# JWT配置
jwt:
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期(分钟)
  expire_time: 30
  # 令牌标识
  header: Authorization

# 日志配置
log:
  # 日志级别 debug, info, warn, error, dpanic, panic, fatal
  level: debug
  # 日志文件名
  filename: logs/ruoyi-go.log
  # 单个日志文件的最大尺寸(MB)
  max_size: 100
  # 保留的旧日志文件的最大数量
  max_backups: 10
  # 保留的旧日志文件的最大天数
  max_age: 30
  # 是否压缩旧日志文件
  compress: true 