# RuoYi框架分析规则

本文档定义了RuoYi框架Java到Go迁移过程中的代码分析规则和标准，确保分析的完整性和一致性。

## 1. 代码分析规则

### 1.1 模块分析规则

每个模块的分析应包含以下内容：

- **模块名称**：模块的名称和简短描述
- **功能概述**：模块的主要功能和职责
- **依赖关系**：模块依赖的其他模块和外部库
- **核心类**：模块中的核心类及其功能
- **业务流程**：主要业务流程和处理逻辑
- **特殊处理**：模块中的特殊处理和注意事项
- **迁移建议**：模块迁移到Go的建议和注意事项

### 1.2 API分析规则

每个API接口的分析应包含以下内容：

- **接口路径**：API的完整路径
- **请求方法**：GET、POST、PUT、DELETE等
- **请求参数**：参数名称、类型、是否必须、说明
- **响应格式**：响应的数据结构和字段说明
- **权限要求**：访问接口所需的权限
- **业务逻辑**：接口的主要业务逻辑
- **特殊处理**：接口中的特殊处理和注意事项

### 1.3 数据模型分析规则

每个数据模型的分析应包含以下内容：

- **模型名称**：模型的名称
- **表名**：对应的数据库表名
- **字段列表**：字段名、类型、约束、说明
- **关联关系**：与其他模型的关联关系
- **验证规则**：数据验证规则
- **特殊处理**：模型中的特殊处理和注意事项
- **GORM映射建议**：模型映射到GORM的建议

### 1.4 业务逻辑分析规则

每个业务逻辑的分析应包含以下内容：

- **服务名称**：服务的名称
- **功能概述**：服务的主要功能和职责
- **方法列表**：主要方法及其功能
- **事务处理**：事务的使用和管理
- **缓存处理**：缓存的使用和管理
- **异常处理**：异常的处理方式
- **特殊逻辑**：特殊的业务逻辑和处理流程

## 2. 文档命名规范

### 2.1 模块分析文档

文件名格式：`module_[模块名].md`

例如：`module_system.md`, `module_framework.md`

### 2.2 API分析文档

文件名格式：`api_[模块名]_[功能名].md`

例如：`api_system_user.md`, `api_system_role.md`

### 2.3 数据模型分析文档

文件名格式：`model_[模型名].md`

例如：`model_sys_user.md`, `model_sys_role.md`

### 2.4 业务逻辑分析文档

文件名格式：`service_[服务名].md`

例如：`service_sys_user.md`, `service_sys_role.md`

## 3. 分析深度要求

### 3.1 核心模块

核心模块（用户、角色、菜单、部门等）需要进行深度分析，包括：

- 完整的代码流程分析
- 详细的业务逻辑分析
- 完整的数据模型分析
- 详细的API接口分析
- 详细的权限控制分析

### 3.2 非核心模块

非核心模块可以进行适度分析，包括：

- 基本的功能概述
- 主要的业务逻辑分析
- 主要的数据模型分析
- 主要的API接口分析

## 4. 分析优先级

1. **最高优先级**：用户、角色、菜单、权限相关模块
2. **高优先级**：部门、岗位、字典、配置相关模块
3. **中优先级**：日志、通知、在线用户相关模块
4. **低优先级**：代码生成、监控、任务调度相关模块

## 5. 分析质量检查

每个分析文档完成后，需要进行以下质量检查：

- **完整性**：是否包含所有必要的信息
- **准确性**：信息是否准确无误
- **清晰性**：描述是否清晰易懂
- **一致性**：是否与其他文档保持一致
- **实用性**：是否对迁移工作有实际帮助

## 6. 分析过程中的注意事项

- 关注代码中的注释，特别是业务逻辑相关的注释
- 关注异常处理和边界条件处理
- 关注事务管理和并发控制
- 关注权限控制和安全处理
- 关注性能优化和缓存使用
- 记录分析过程中发现的问题和疑问 