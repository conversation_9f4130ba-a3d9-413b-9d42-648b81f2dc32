package service

import (
	"context"
	"fmt"
	"time"

	"github.com/ruoyi-go/internal/utils"
)

// CacheService 缓存服务接口
type CacheService interface {
	// 获取缓存
	GetCache(key string) (interface{}, bool)
	GetCacheString(key string) (string, bool)
	GetCacheInt(key string) (int, bool)
	GetCacheBool(key string) (bool, bool)

	// 设置缓存
	SetCache(key string, value interface{}, expire time.Duration) error

	// 删除缓存
	DeleteCache(key string) error
	DeleteCachePattern(pattern string) error

	// 集合操作
	AddCacheSet(key string, value interface{}, expire time.Duration) error
	GetCacheSet(key string) ([]interface{}, bool)
	RemoveCacheSet(key string, value interface{}) error
	HasCacheSet(key string, value interface{}) (bool, error)

	// 锁操作
	SetCacheLock(key string, expire time.Duration) (bool, error)
	ReleaseCacheLock(key string) error

	// 业务缓存
	GetSysConfig(ctx context.Context, configKey string) (string, error)
	SetSysConfig(ctx context.Context, configKey string, configValue string) error
	DeleteSysConfig(ctx context.Context, configKey string) error

	GetSysDict(ctx context.Context, dictType string, dictValue string) (string, error)
	SetSysDict(ctx context.Context, dictType string, dictValue string, dictLabel string) error
	DeleteSysDict(ctx context.Context, dictType string) error

	ResetCache(ctx context.Context) error
	ClearAll(ctx context.Context) error
}

// cacheServiceImpl 缓存服务实现
type cacheServiceImpl struct {
	cache         *utils.CacheUtils
	configService ConfigService
	dictService   DictService
}

// NewCacheService 创建缓存服务
func NewCacheService() CacheService {
	return &cacheServiceImpl{
		cache: utils.NewCacheUtils(),
	}
}

// SetConfigService 设置配置服务
func (s *cacheServiceImpl) SetConfigService(configService ConfigService) {
	s.configService = configService
}

// SetDictService 设置字典服务
func (s *cacheServiceImpl) SetDictService(dictService DictService) {
	s.dictService = dictService
}

// GetCache 获取缓存
func (s *cacheServiceImpl) GetCache(key string) (interface{}, bool) {
	return s.cache.Get(key)
}

// GetCacheString 获取字符串缓存
func (s *cacheServiceImpl) GetCacheString(key string) (string, bool) {
	return s.cache.GetString(key)
}

// GetCacheInt 获取整数缓存
func (s *cacheServiceImpl) GetCacheInt(key string) (int, bool) {
	return s.cache.GetInt(key)
}

// GetCacheBool 获取布尔缓存
func (s *cacheServiceImpl) GetCacheBool(key string) (bool, bool) {
	return s.cache.GetBool(key)
}

// SetCache 设置缓存
func (s *cacheServiceImpl) SetCache(key string, value interface{}, expire time.Duration) error {
	return s.cache.Set(key, value, expire)
}

// DeleteCache 删除缓存
func (s *cacheServiceImpl) DeleteCache(key string) error {
	return s.cache.Delete(key)
}

// DeleteCachePattern 根据前缀删除缓存
func (s *cacheServiceImpl) DeleteCachePattern(pattern string) error {
	return s.cache.DeletePattern(pattern)
}

// AddCacheSet 添加到集合
func (s *cacheServiceImpl) AddCacheSet(key string, value interface{}, expire time.Duration) error {
	return s.cache.AddSet(key, value, expire)
}

// GetCacheSet 获取集合
func (s *cacheServiceImpl) GetCacheSet(key string) ([]interface{}, bool) {
	return s.cache.GetSet(key)
}

// RemoveCacheSet 从集合中移除
func (s *cacheServiceImpl) RemoveCacheSet(key string, value interface{}) error {
	return s.cache.RemoveSet(key, value)
}

// HasCacheSet 判断集合是否包含元素
func (s *cacheServiceImpl) HasCacheSet(key string, value interface{}) (bool, error) {
	return s.cache.HasSet(key, value)
}

// SetCacheLock 获取锁
func (s *cacheServiceImpl) SetCacheLock(key string, expire time.Duration) (bool, error) {
	return s.cache.SetLock(key, expire)
}

// ReleaseCacheLock 释放锁
func (s *cacheServiceImpl) ReleaseCacheLock(key string) error {
	return s.cache.ReleaseLock(key)
}

// GetSysConfig 获取系统配置缓存
func (s *cacheServiceImpl) GetSysConfig(ctx context.Context, configKey string) (string, error) {
	// 构建缓存键
	cacheKey := utils.SYS_CONFIG_KEY + configKey

	// 尝试从缓存获取
	value, found := s.GetCacheString(cacheKey)
	if found {
		return value, nil
	}

	// 缓存未命中，从数据库获取
	if s.configService == nil {
		return "", fmt.Errorf("config service not initialized")
	}

	config, err := s.configService.GetConfigByKey(ctx, configKey)
	if err != nil {
		return "", err
	}

	if config == nil {
		return "", fmt.Errorf("config not found: %s", configKey)
	}

	// 存入缓存
	s.SetCache(cacheKey, config.ConfigValue, 24*time.Hour)

	return config.ConfigValue, nil
}

// SetSysConfig 设置系统配置缓存
func (s *cacheServiceImpl) SetSysConfig(ctx context.Context, configKey string, configValue string) error {
	// 构建缓存键
	cacheKey := utils.SYS_CONFIG_KEY + configKey

	// 设置缓存
	return s.SetCache(cacheKey, configValue, 24*time.Hour)
}

// DeleteSysConfig 删除系统配置缓存
func (s *cacheServiceImpl) DeleteSysConfig(ctx context.Context, configKey string) error {
	// 构建缓存键
	cacheKey := utils.SYS_CONFIG_KEY + configKey

	// 删除缓存
	return s.DeleteCache(cacheKey)
}

// GetSysDict 获取字典缓存
func (s *cacheServiceImpl) GetSysDict(ctx context.Context, dictType string, dictValue string) (string, error) {
	// 构建缓存键
	cacheKey := utils.SYS_DICT_KEY + dictType + ":" + dictValue

	// 尝试从缓存获取
	value, found := s.GetCacheString(cacheKey)
	if found {
		return value, nil
	}

	// 缓存未命中，从数据库获取
	if s.dictService == nil {
		return "", fmt.Errorf("dict service not initialized")
	}

	dictData, err := s.dictService.GetDictDataByTypeAndValue(ctx, dictType, dictValue)
	if err != nil {
		return "", err
	}

	if dictData == nil {
		return "", fmt.Errorf("dict data not found: %s, %s", dictType, dictValue)
	}

	// 存入缓存
	s.SetCache(cacheKey, dictData.DictLabel, 24*time.Hour)

	return dictData.DictLabel, nil
}

// SetSysDict 设置字典缓存
func (s *cacheServiceImpl) SetSysDict(ctx context.Context, dictType string, dictValue string, dictLabel string) error {
	// 构建缓存键
	cacheKey := utils.SYS_DICT_KEY + dictType + ":" + dictValue

	// 设置缓存
	return s.SetCache(cacheKey, dictLabel, 24*time.Hour)
}

// DeleteSysDict 删除字典缓存
func (s *cacheServiceImpl) DeleteSysDict(ctx context.Context, dictType string) error {
	// 构建缓存键前缀
	cacheKeyPattern := utils.SYS_DICT_KEY + dictType + ":"

	// 删除缓存
	return s.DeleteCachePattern(cacheKeyPattern)
}

// ResetCache 重置缓存
func (s *cacheServiceImpl) ResetCache(ctx context.Context) error {
	// 清除系统配置缓存
	if err := s.DeleteCachePattern(utils.SYS_CONFIG_KEY); err != nil {
		return err
	}

	// 清除字典缓存
	if err := s.DeleteCachePattern(utils.SYS_DICT_KEY); err != nil {
		return err
	}

	return nil
}

// ClearAll 清空所有缓存
func (s *cacheServiceImpl) ClearAll(ctx context.Context) error {
	cache, ok := utils.GetCache().(*utils.MemoryCache)
	if !ok {
		return fmt.Errorf("unsupported cache type")
	}

	return cache.Clear()
}
