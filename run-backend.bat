@echo off
echo ==================================
echo       若依系统后端启动脚本
echo ==================================

echo 检查环境...

REM 检查MySQL数据库是否配置
mysql --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] MySQL未安装或未添加到PATH中
    echo 请先安装MySQL并运行setup-database.bat配置数据库
    goto :end
)

REM 检查数据库是否存在
echo 正在检查数据库...
mysql -uroot -e "SHOW DATABASES LIKE 'ry-vue'" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 数据库'ry-vue'不存在
    echo 请先运行setup-database.bat配置数据库
    goto :end
)

REM 设置Maven路径
set MAVEN_HOME=D:\tools\apache-maven-3.9.10
set PATH=%MAVEN_HOME%\bin;%PATH%

echo 1. 检查Maven是否可用...
call mvn -version
if %ERRORLEVEL% NEQ 0 (
    echo Maven不可用，请确保Maven已正确安装并设置环境变量
    goto :end
)

echo 2. 检查Java是否可用...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo Java不可用，请确保已安装JDK并设置环境变量
    goto :end
)

echo 3. 编译项目...
cd ruoyi-java
call mvn clean package -DskipTests
if %ERRORLEVEL% NEQ 0 (
    echo 编译失败，请检查错误信息
    goto :end
)

echo 4. 启动后端服务...
cd ruoyi-admin\target
echo 当前目录: %CD%
echo 正在启动ruoyi-admin.jar...
java -jar ruoyi-admin.jar

:end
pause 