package model

import (
	"time"
)

// BaseModel 是所有模型的基础结构
type BaseModel struct {
	CreateBy   string    `json:"createBy" gorm:"column:create_by;comment:创建者"`
	CreateTime time.Time `json:"createTime" gorm:"column:create_time;comment:创建时间"`
	UpdateBy   string    `json:"updateBy" gorm:"column:update_by;comment:更新者"`
	UpdateTime time.Time `json:"updateTime" gorm:"column:update_time;comment:更新时间"`
	Remark     string    `json:"remark" gorm:"column:remark;type:varchar(500);comment:备注"`

	// 非数据库字段
	SearchValue string                 `json:"searchValue" gorm:"-"` // 搜索值
	Params      map[string]interface{} `json:"params" gorm:"-"`      // 请求参数
}

// BaseEntity 基础实体（不包含删除标志，只有部分实体需要删除标志）
type BaseEntity struct {
	BaseModel
}
