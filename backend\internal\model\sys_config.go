package model

import (
	"time"
)

// SysConfig 参数配置表 sys_config
type SysConfig struct {
	// 参数主键
	ConfigId uint64 `gorm:"primary_key;column:config_id" json:"configId"`
	// 参数名称
	ConfigName string `gorm:"column:config_name" json:"configName"`
	// 参数键名
	ConfigKey string `gorm:"column:config_key" json:"configKey"`
	// 参数键值
	ConfigValue string `gorm:"column:config_value" json:"configValue"`
	// 系统内置（Y是 N否）
	ConfigType string `gorm:"column:config_type" json:"configType"`
	// 创建者
	CreateBy string `gorm:"column:create_by" json:"createBy"`
	// 创建时间
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	// 更新者
	UpdateBy string `gorm:"column:update_by" json:"updateBy"`
	// 更新时间
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	// 备注
	Remark string `gorm:"column:remark" json:"remark"`
}

// TableName 表名
func (SysConfig) TableName() string {
	return "sys_config"
}
