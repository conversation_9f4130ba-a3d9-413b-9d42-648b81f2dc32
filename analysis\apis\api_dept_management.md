# 部门管理API接口分析

## 模块概述

部门管理模块负责系统组织架构的管理，是用户归属和数据权限控制的基础。主要功能包括部门的增删改查，以及部门树的构建。

## API接口列表

### 1. 部门列表查询

- **接口路径**：`/system/dept/list`
- **请求方法**：GET
- **接口说明**：查询部门列表，支持多条件筛选
- **权限标识**：`system:dept:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| deptName | string | 否 | 部门名称 |
| status | string | 否 | 状态（0正常 1停用） |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "deptId": 100,
      "parentId": 0,
      "ancestors": "0",
      "deptName": "总公司",
      "orderNum": 0,
      "leader": "张三",
      "phone": "15888888888",
      "email": "<EMAIL>",
      "status": "0",
      "createTime": "2023-01-01 12:00:00",
      "children": [
        {
          "deptId": 101,
          "parentId": 100,
          "ancestors": "0,100",
          "deptName": "研发部门",
          "orderNum": 1,
          "leader": "李四",
          "phone": "15888888889",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2023-01-01 12:00:00",
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 获取部门详情

- **接口路径**：`/system/dept/{deptId}`
- **请求方法**：GET
- **接口说明**：根据部门ID查询部门详细信息
- **权限标识**：`system:dept:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| deptId | integer | 是 | 部门ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "deptId": 101,
    "parentId": 100,
    "ancestors": "0,100",
    "deptName": "研发部门",
    "orderNum": 1,
    "leader": "李四",
    "phone": "15888888889",
    "email": "<EMAIL>",
    "status": "0"
  }
}
```

### 3. 获取部门下拉树列表

- **接口路径**：`/system/dept/treeselect`
- **请求方法**：GET
- **接口说明**：获取部门下拉树列表
- **权限标识**：`system:dept:query`
- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 100,
      "label": "总公司",
      "children": [
        {
          "id": 101,
          "label": "研发部门",
          "children": []
        }
      ]
    }
  ]
}
```

### 4. 获取角色部门树列表

- **接口路径**：`/system/dept/roleDeptTreeselect/{roleId}`
- **请求方法**：GET
- **接口说明**：获取角色部门树列表
- **权限标识**：`system:dept:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| roleId | integer | 是 | 角色ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "checkedKeys": [100, 101],
    "depts": [
      {
        "id": 100,
        "label": "总公司",
        "children": [
          {
            "id": 101,
            "label": "研发部门",
            "children": []
          }
        ]
      }
    ]
  }
}
```

### 5. 新增部门

- **接口路径**：`/system/dept`
- **请求方法**：POST
- **接口说明**：新增部门信息
- **权限标识**：`system:dept:add`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| parentId | integer | 是 | 父部门ID |
| deptName | string | 是 | 部门名称 |
| orderNum | integer | 是 | 显示顺序 |
| leader | string | 否 | 负责人 |
| phone | string | 否 | 联系电话 |
| email | string | 否 | 邮箱 |
| status | string | 否 | 部门状态（0正常 1停用） |

- **请求示例**：
```json
{
  "parentId": 100,
  "deptName": "测试部门",
  "orderNum": 3,
  "leader": "王五",
  "phone": "15888888890",
  "email": "<EMAIL>",
  "status": "0"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

### 6. 修改部门

- **接口路径**：`/system/dept`
- **请求方法**：PUT
- **接口说明**：修改部门信息
- **权限标识**：`system:dept:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| deptId | integer | 是 | 部门ID |
| parentId | integer | 是 | 父部门ID |
| deptName | string | 是 | 部门名称 |
| orderNum | integer | 是 | 显示顺序 |
| leader | string | 否 | 负责人 |
| phone | string | 否 | 联系电话 |
| email | string | 否 | 邮箱 |
| status | string | 否 | 部门状态（0正常 1停用） |

- **请求示例**：
```json
{
  "deptId": 102,
  "parentId": 100,
  "deptName": "测试部门修改",
  "orderNum": 4,
  "leader": "赵六",
  "phone": "15888888891",
  "email": "<EMAIL>",
  "status": "0"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 7. 删除部门

- **接口路径**：`/system/dept/{deptId}`
- **请求方法**：DELETE
- **接口说明**：删除部门信息
- **权限标识**：`system:dept:remove`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| deptId | integer | 是 | 部门ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

## 接口实现注意事项

1. **部门层级关系**：
   - 部门是树形结构，需要处理好父子关系
   - 查询列表时通常需要返回树形结构
   - 祖级列表（ancestors）是用逗号分隔的祖先部门ID字符串，用于快速查找上级部门

2. **部门约束**：
   - 同一个父部门下，部门名称不能重复
   - 不能删除有子部门的部门
   - 不能删除有用户的部门

3. **父部门更新**：
   - 修改部门的父ID时，需要同时更新其祖级列表（ancestors）
   - 如果有子部门，还需要级联更新所有子部门的祖级列表
   - 需要确保不会产生循环引用（如不能将部门的子部门设为其父部门）

4. **部门状态**：
   - 停用部门后，通常需要级联停用其所有子部门
   - 停用的部门下的用户通常也会受到影响

5. **数据权限**：
   - 部门结构是实现数据权限控制的基础
   - 不同角色可以设置不同的数据权限范围（如本部门、本部门及以下、自定义部门等）

## Gin+GORM实现建议

1. **Controller层**：
```go
// DeptController 处理部门相关请求
type DeptController struct {
    DeptService service.IDeptService
}

// List 获取部门列表
func (c *DeptController) List(ctx *gin.Context) {
    // 解析查询参数
    deptName := ctx.Query("deptName")
    status := ctx.Query("status")
    
    // 调用Service层获取数据
    depts, err := c.DeptService.SelectDeptList(ctx, deptName, status)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    // 返回结果
    response.Success(ctx, depts)
}

// GetInfo 获取部门详情
func (c *DeptController) GetInfo(ctx *gin.Context) {
    deptId, err := strconv.ParseUint(ctx.Param("deptId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    dept, err := c.DeptService.SelectDeptById(ctx, deptId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    response.Success(ctx, dept)
}

// TreeSelect 获取部门下拉树列表
func (c *DeptController) TreeSelect(ctx *gin.Context) {
    // 调用Service层获取部门树
    depts, err := c.DeptService.SelectDeptTreeAll(ctx)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    response.Success(ctx, depts)
}

// RoleDeptTreeSelect 获取角色部门树列表
func (c *DeptController) RoleDeptTreeSelect(ctx *gin.Context) {
    roleId, err := strconv.ParseUint(ctx.Param("roleId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 获取所有部门树
    depts, err := c.DeptService.SelectDeptTreeAll(ctx)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询部门树失败")
        return
    }
    
    // 获取角色已选中的部门ID
    checkedKeys, err := c.DeptService.SelectDeptListByRoleId(ctx, roleId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询角色部门失败")
        return
    }
    
    response.Success(ctx, gin.H{
        "depts":       depts,
        "checkedKeys": checkedKeys,
    })
}

// Add 添加部门
func (c *DeptController) Add(ctx *gin.Context) {
    var req request.DeptAddRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 验证部门名称唯一性
    if c.DeptService.CheckDeptNameUnique(ctx, req.ParentId, req.DeptName) {
        response.Error(ctx, http.StatusBadRequest, "部门名称已存在")
        return
    }
    
    // 创建部门
    err := c.DeptService.InsertDept(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "新增部门失败")
        return
    }
    
    response.Success(ctx, nil)
}

// Update 修改部门
func (c *DeptController) Update(ctx *gin.Context) {
    var req request.DeptUpdateRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 验证部门是否存在
    dept, err := c.DeptService.SelectDeptById(ctx, req.DeptId)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "部门不存在")
        return
    }
    
    // 验证是否选择自己或自己的子部门作为父部门
    if req.DeptId == req.ParentId {
        response.Error(ctx, http.StatusBadRequest, "父部门不能选择自己")
        return
    }
    
    if c.DeptService.IsChildOfDept(ctx, req.DeptId, req.ParentId) {
        response.Error(ctx, http.StatusBadRequest, "父部门不能选择子部门")
        return
    }
    
    // 验证部门名称唯一性（排除自身）
    if req.DeptName != dept.DeptName && c.DeptService.CheckDeptNameUnique(ctx, req.ParentId, req.DeptName) {
        response.Error(ctx, http.StatusBadRequest, "部门名称已存在")
        return
    }
    
    // 更新部门
    err = c.DeptService.UpdateDept(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "修改部门失败")
        return
    }
    
    response.Success(ctx, nil)
}

// Delete 删除部门
func (c *DeptController) Delete(ctx *gin.Context) {
    deptId, err := strconv.ParseUint(ctx.Param("deptId"), 10, 64)
    if err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 验证是否存在子部门
    if c.DeptService.HasChildDept(ctx, deptId) {
        response.Error(ctx, http.StatusBadRequest, "存在下级部门，不允许删除")
        return
    }
    
    // 验证是否存在用户
    if c.DeptService.HasDeptUser(ctx, deptId) {
        response.Error(ctx, http.StatusBadRequest, "部门存在用户，不允许删除")
        return
    }
    
    // 删除部门
    err = c.DeptService.DeleteDeptById(ctx, deptId)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "删除部门失败")
        return
    }
    
    response.Success(ctx, nil)
}
```

2. **Service层**：
```go
// IDeptService 部门服务接口
type IDeptService interface {
    SelectDeptList(ctx *gin.Context, deptName, status string) ([]*models.SysDept, error)
    SelectDeptById(ctx *gin.Context, deptId uint64) (*models.SysDept, error)
    InsertDept(ctx *gin.Context, req request.DeptAddRequest) error
    UpdateDept(ctx *gin.Context, req request.DeptUpdateRequest) error
    DeleteDeptById(ctx *gin.Context, deptId uint64) error
    CheckDeptNameUnique(ctx *gin.Context, parentId uint64, deptName string) bool
    HasChildDept(ctx *gin.Context, deptId uint64) bool
    HasDeptUser(ctx *gin.Context, deptId uint64) bool
    IsChildOfDept(ctx *gin.Context, deptId, parentId uint64) bool
    SelectDeptTreeAll(ctx *gin.Context) ([]*models.TreeSelect, error)
    SelectDeptListByRoleId(ctx *gin.Context, roleId uint64) ([]uint64, error)
    BuildDeptTree(depts []*models.SysDept) []*models.SysDept
    BuildTreeSelect(depts []*models.SysDept) []*models.TreeSelect
    // 其他方法...
}

// DeptService 部门服务实现
type DeptService struct {
    DB *gorm.DB
}

// SelectDeptList 查询部门列表
func (s *DeptService) SelectDeptList(ctx *gin.Context, deptName, status string) ([]*models.SysDept, error) {
    var depts []*models.SysDept
    
    db := s.DB.Model(&models.SysDept{}).Where("del_flag = ?", "0")
    
    // 添加查询条件
    if deptName != "" {
        db = db.Where("dept_name like ?", "%"+deptName+"%")
    }
    
    if status != "" {
        db = db.Where("status = ?", status)
    }
    
    // 数据权限过滤
    db = s.dataScopeFilter(ctx, db)
    
    // 排序查询
    if err := db.Order("parent_id, order_num").Find(&depts).Error; err != nil {
        return nil, err
    }
    
    // 构建树形结构
    return s.BuildDeptTree(depts), nil
}

// InsertDept 新增部门
func (s *DeptService) InsertDept(ctx *gin.Context, req request.DeptAddRequest) error {
    // 获取当前用户
    currentUser := auth.GetCurrentUser(ctx)
    
    // 查询父部门信息，获取ancestors
    var parentAncestors string
    if req.ParentId == 0 {
        parentAncestors = "0"
    } else {
        var parent models.SysDept
        if err := s.DB.Where("dept_id = ?", req.ParentId).First(&parent).Error; err != nil {
            return err
        }
        parentAncestors = parent.Ancestors
    }
    
    // 创建部门
    dept := &models.SysDept{
        ParentId:   req.ParentId,
        DeptName:   req.DeptName,
        OrderNum:   req.OrderNum,
        Leader:     req.Leader,
        Phone:      req.Phone,
        Email:      req.Email,
        Status:     req.Status,
        CreateBy:   currentUser.UserName,
        CreateTime: time.Now(),
    }
    
    // 保存到数据库，并获取新生成的ID
    if err := s.DB.Create(dept).Error; err != nil {
        return err
    }
    
    // 更新ancestors
    dept.Ancestors = parentAncestors + "," + strconv.FormatUint(dept.DeptId, 10)
    return s.DB.Model(dept).Update("ancestors", dept.Ancestors).Error
}

// UpdateDept 修改部门
func (s *DeptService) UpdateDept(ctx *gin.Context, req request.DeptUpdateRequest) error {
    // 获取当前用户
    currentUser := auth.GetCurrentUser(ctx)
    
    // 查询原部门信息
    var oldDept models.SysDept
    if err := s.DB.Where("dept_id = ?", req.DeptId).First(&oldDept).Error; err != nil {
        return err
    }
    
    // 更新部门信息
    dept := &models.SysDept{
        DeptId:     req.DeptId,
        ParentId:   req.ParentId,
        DeptName:   req.DeptName,
        OrderNum:   req.OrderNum,
        Leader:     req.Leader,
        Phone:      req.Phone,
        Email:      req.Email,
        Status:     req.Status,
        UpdateBy:   currentUser.UserName,
        UpdateTime: time.Now(),
    }
    
    // 判断是否修改了父部门
    if oldDept.ParentId != req.ParentId {
        // 查询新父部门信息，获取ancestors
        var newParentAncestors string
        if req.ParentId == 0 {
            newParentAncestors = "0"
        } else {
            var parent models.SysDept
            if err := s.DB.Where("dept_id = ?", req.ParentId).First(&parent).Error; err != nil {
                return err
            }
            newParentAncestors = parent.Ancestors
        }
        
        // 更新ancestors
        newAncestors := newParentAncestors + "," + strconv.FormatUint(dept.DeptId, 10)
        oldAncestors := oldDept.Ancestors
        
        // 开启事务
        return s.DB.Transaction(func(tx *gorm.DB) error {
            // 更新部门信息
            if err := tx.Model(dept).Updates(dept).Error; err != nil {
                return err
            }
            
            // 更新部门ancestors
            if err := tx.Model(dept).Update("ancestors", newAncestors).Error; err != nil {
                return err
            }
            
            // 更新子部门的ancestors
            return tx.Model(&models.SysDept{}).Where("ancestors like ?", oldAncestors+","+strconv.FormatUint(dept.DeptId, 10)+"%").
                UpdateColumn("ancestors", gorm.Expr("REPLACE(ancestors, ?, ?)", oldAncestors, newAncestors)).Error
        })
    } else {
        // 未修改父部门，直接更新部门信息
        return s.DB.Model(dept).Updates(dept).Error
    }
}

// BuildDeptTree 构建部门树
func (s *DeptService) BuildDeptTree(depts []*models.SysDept) []*models.SysDept {
    // 创建根节点
    var roots []*models.SysDept
    // 创建节点映射，便于查找父节点
    deptMap := make(map[uint64]*models.SysDept)
    
    // 将所有节点存入map
    for _, dept := range depts {
        // 为每个节点创建空的子节点切片
        dept.Children = []*models.SysDept{}
        deptMap[dept.DeptId] = dept
    }
    
    // 构建树形结构
    for _, dept := range depts {
        if dept.ParentId == 0 {
            // 如果是根节点，直接添加到roots
            roots = append(roots, dept)
        } else {
            // 如果有父节点，添加到父节点的children
            if parent, exists := deptMap[dept.ParentId]; exists {
                parent.Children = append(parent.Children, dept)
            }
        }
    }
    
    return roots
}

// 其他方法...

// dataScopeFilter 数据权限过滤
func (s *DeptService) dataScopeFilter(ctx *gin.Context, db *gorm.DB) *gorm.DB {
    // 获取当前用户
    currentUser := auth.GetCurrentUser(ctx)
    
    // 如果是管理员，不进行数据过滤
    if currentUser.IsAdmin() {
        return db
    }
    
    // 获取用户的角色数据权限范围
    dataScope := s.getUserDataScope(currentUser.UserId)
    
    // 根据数据范围类型进行过滤
    switch dataScope {
    case "1": // 全部数据权限
        return db
    case "2": // 自定数据权限
        return db.Where("dept_id in (?)", s.getRoleDeptIds(currentUser.UserId))
    case "3": // 本部门数据权限
        return db.Where("dept_id = ?", currentUser.DeptId)
    case "4": // 本部门及以下数据权限
        return db.Where("ancestors like ?", "%,"+strconv.FormatUint(currentUser.DeptId, 10)+",%").
            Or("dept_id = ?", currentUser.DeptId)
    case "5": // 仅本人数据权限
        // 通常部门管理不会用到"仅本人"的数据权限，这里为了完整性保留
        return db.Where("create_by = ?", currentUser.UserName)
    default:
        return db
    }
}
```

3. **请求和响应结构**：
```go
// DeptAddRequest 新增部门请求
type DeptAddRequest struct {
    ParentId  uint64 `json:"parentId"`
    DeptName  string `json:"deptName" binding:"required"`
    OrderNum  int    `json:"orderNum" binding:"required"`
    Leader    string `json:"leader"`
    Phone     string `json:"phone"`
    Email     string `json:"email"`
    Status    string `json:"status"`
}

// DeptUpdateRequest 修改部门请求
type DeptUpdateRequest struct {
    DeptId    uint64 `json:"deptId" binding:"required"`
    ParentId  uint64 `json:"parentId"`
    DeptName  string `json:"deptName" binding:"required"`
    OrderNum  int    `json:"orderNum" binding:"required"`
    Leader    string `json:"leader"`
    Phone     string `json:"phone"`
    Email     string `json:"email"`
    Status    string `json:"status"`
}
```

4. **路由注册**：
```go
// 注册部门管理路由
func RegisterDeptRoutes(r *gin.RouterGroup, deptController *controller.DeptController) {
    deptRouter := r.Group("/system/dept")
    {
        deptRouter.GET("/list", middleware.Auth(), middleware.HasPermission("system:dept:list"), deptController.List)
        deptRouter.GET("/:deptId", middleware.Auth(), middleware.HasPermission("system:dept:query"), deptController.GetInfo)
        deptRouter.GET("/treeselect", middleware.Auth(), middleware.HasPermission("system:dept:query"), deptController.TreeSelect)
        deptRouter.GET("/roleDeptTreeselect/:roleId", middleware.Auth(), middleware.HasPermission("system:dept:query"), deptController.RoleDeptTreeSelect)
        deptRouter.POST("", middleware.Auth(), middleware.HasPermission("system:dept:add"), deptController.Add)
        deptRouter.PUT("", middleware.Auth(), middleware.HasPermission("system:dept:edit"), deptController.Update)
        deptRouter.DELETE("/:deptId", middleware.Auth(), middleware.HasPermission("system:dept:remove"), deptController.Delete)
    }
} 