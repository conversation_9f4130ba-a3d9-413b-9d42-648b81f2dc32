package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi-go/config"
	"github.com/ruoyi-go/pkg/auth"
	"github.com/ruoyi-go/pkg/logger"
	"github.com/ruoyi-go/pkg/response"
)

// Cors 跨域中间件
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method

		c.Header("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Headers", "Content-Type, AccessToken, X-CSRF-Token, Authorization, Token")
		c.<PERSON>er("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.<PERSON>er("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestLog 请求日志中间件
func RequestLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 结束时间
		endTime := time.Now()

		// 执行时间
		latencyTime := endTime.Sub(startTime)

		// 请求方式
		reqMethod := c.Request.Method

		// 请求路由
		reqUri := c.Request.RequestURI

		// 状态码
		statusCode := c.Writer.Status()

		// 请求IP
		clientIP := c.ClientIP()

		// 日志格式
		logger.Info("REQUEST",
			logger.String("method", reqMethod),
			logger.String("uri", reqUri),
			logger.Int("status", statusCode),
			logger.String("ip", clientIP),
			logger.Duration("latency", latencyTime),
		)
	}
}

// Recovery 异常恢复中间件
func Recovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				logger.Error("PANIC RECOVERED",
					logger.Any("error", err),
					logger.String("url", c.Request.URL.Path),
					logger.String("method", c.Request.Method),
				)
				response.Fail(c, response.ERROR, "服务器内部错误")
				c.Abort()
			}
		}()
		c.Next()
	}
}

// JWT JWT认证中间件
func JWT() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization header
		tokenHeader := c.GetHeader(config.AppConfig.JWT.Header)
		if tokenHeader == "" {
			response.Fail(c, response.UNAUTHORIZED, "未登录或非法访问")
			c.Abort()
			return
		}

		// 检查token格式
		parts := strings.SplitN(tokenHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			response.Fail(c, response.UNAUTHORIZED, "认证格式有误")
			c.Abort()
			return
		}

		// 解析token
		claims, err := auth.ParseToken(parts[1])
		if err != nil {
			response.Fail(c, response.UNAUTHORIZED, "认证失败: "+err.Error())
			c.Abort()
			return
		}

		// 将用户信息保存到上下文
		c.Set("userId", claims.UserId)
		c.Set("username", claims.Username)
		c.Set("roleIds", claims.RoleIds)
		c.Set("permissions", claims.Permissions)

		c.Next()
	}
}
