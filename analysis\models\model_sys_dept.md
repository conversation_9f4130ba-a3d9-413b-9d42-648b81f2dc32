# 部门模型分析

## 模型名称

`SysDept`（系统部门）

## 表名

`sys_dept`

## 字段列表

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| dept_id | bigint | 部门ID | 主键 |
| parent_id | bigint | 父部门ID | 默认0 |
| ancestors | varchar(50) | 祖级列表 | 可空 |
| dept_name | varchar(30) | 部门名称 | 非空 |
| order_num | int | 显示顺序 | 默认0 |
| leader | varchar(20) | 负责人 | 可空 |
| phone | varchar(11) | 联系电话 | 可空 |
| email | varchar(50) | 邮箱 | 可空 |
| status | char(1) | 部门状态（0正常 1停用） | 默认'0' |
| del_flag | char(1) | 删除标志（0代表存在 2代表删除） | 默认'0' |
| create_by | varchar(64) | 创建者 | 可空 |
| create_time | datetime | 创建时间 | 可空 |
| update_by | varchar(64) | 更新者 | 可空 |
| update_time | datetime | 更新时间 | 可空 |

## Java实体类结构

```java
public class SysDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 部门ID */
    private Long deptId;

    /** 父部门ID */
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 部门名称 */
    private String deptName;

    /** 显示顺序 */
    private Integer orderNum;

    /** 负责人 */
    private String leader;

    /** 联系电话 */
    private String phone;

    /** 邮箱 */
    private String email;

    /** 部门状态:0正常,1停用 */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 父部门名称 */
    private String parentName;
    
    /** 子部门 */
    private List<SysDept> children = new ArrayList<SysDept>();
    
    // getter/setter方法...
}
```

## 关联关系

1. **部门-用户**：一对多关系，一个部门可以有多个用户，一个用户只属于一个部门
   - 关联字段：用户表中的`dept_id`
   - 关联实体：`SysUser`

2. **部门-部门**：自引用关系，一个部门可以有多个子部门，一个子部门只有一个父部门
   - 关联字段：`parent_id`
   - 关联实体：`SysDept`（自身）

3. **部门-角色**：多对多关系，一个部门可以被多个角色管理（数据权限），一个角色可以管理多个部门
   - 关联表：`sys_role_dept`
   - 关联实体：`SysRole`

## 验证规则

1. `deptName`：部门名称不能为空
2. `orderNum`：显示顺序必须为数字，用于同级部门排序
3. `email`：邮箱格式必须正确（如果提供）
4. `phone`：电话号码格式必须正确（如果提供）
5. 同一级部门下，部门名称不能重复

## 特殊处理

1. 部门结构为树形结构，通过`parent_id`字段确定层级关系
2. 顶级部门的`parent_id`为0
3. 部门的显示顺序由`order_num`字段决定，同级部门按此字段升序排列
4. 删除部门采用逻辑删除，设置`del_flag = '2'`
5. 如果部门下有子部门或用户，通常不允许删除
6. `ancestors`字段存储所有祖级部门的ID，用逗号分隔，例如："0,100,101"
7. 当部门状态为停用时，该部门及其子部门下的用户可能会受到影响

## GORM映射建议

```go
// SysDept 系统部门
type SysDept struct {
    DeptId      uint64    `gorm:"primary_key;column:dept_id;comment:部门ID"`
    ParentId    uint64    `gorm:"default:0;column:parent_id;comment:父部门ID"`
    Ancestors   string    `gorm:"size:50;column:ancestors;comment:祖级列表"`
    DeptName    string    `gorm:"size:30;not null;column:dept_name;comment:部门名称"`
    OrderNum    int       `gorm:"default:0;column:order_num;comment:显示顺序"`
    Leader      string    `gorm:"size:20;column:leader;comment:负责人"`
    Phone       string    `gorm:"size:11;column:phone;comment:联系电话"`
    Email       string    `gorm:"size:50;column:email;comment:邮箱"`
    Status      string    `gorm:"size:1;default:0;column:status;comment:部门状态（0正常 1停用）"`
    DelFlag     string    `gorm:"size:1;default:0;column:del_flag;comment:删除标志（0代表存在 2代表删除）"`
    CreateBy    string    `gorm:"size:64;column:create_by;comment:创建者"`
    CreateTime  time.Time `gorm:"column:create_time;comment:创建时间"`
    UpdateBy    string    `gorm:"size:64;column:update_by;comment:更新者"`
    UpdateTime  time.Time `gorm:"column:update_time;comment:更新时间"`
    
    // 关联字段
    Users       []*SysUser `gorm:"foreignKey:DeptId;references:DeptId"`
    Roles       []*SysRole `gorm:"many2many:sys_role_dept;foreignKey:DeptId;joinForeignKey:DeptId;References:RoleId;JoinReferences:RoleId"`
    Children    []*SysDept `gorm:"-"` // 不映射到数据库，用于构建树形结构
    ParentName  string     `gorm:"-"` // 父部门名称，不映射到数据库
}

// TableName 设置表名
func (SysDept) TableName() string {
    return "sys_dept"
}
```

## 迁移注意事项

1. **数据类型映射**：
   - Java的`Long`对应Go的`uint64`
   - Java的`String`对应Go的`string`
   - Java的`Integer`对应Go的`int`
   - Java的`Date`对应Go的`time.Time`

2. **树形结构处理**：
   - Java版本中使用`children`字段存储子部门
   - Go版本中也使用`Children`字段，但标记为`gorm:"-"`，表示不映射到数据库
   - 需要单独编写方法来构建树形结构

3. **祖级列表处理**：
   - `ancestors`字段是RuoYi框架特有的设计，用于快速查找某个部门的所有上级部门
   - 在增删改操作时需要特别处理这个字段
   - 新增部门时，其`ancestors`值为父部门的`ancestors`加上父部门ID
   - 更新部门时，如果修改了`parent_id`，需要同时更新本部门及所有子部门的`ancestors`

4. **数据权限处理**：
   - 部门是数据权限控制的基础
   - 用户所属部门决定了其可访问的数据范围
   - 需要在查询时根据用户角色和部门进行数据过滤

5. **级联影响**：
   - 部门停用时，通常需要级联停用其所有子部门
   - 部门删除时，需要考虑其子部门和关联用户的处理策略 