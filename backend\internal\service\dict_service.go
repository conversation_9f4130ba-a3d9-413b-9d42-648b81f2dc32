package service

import (
	"context"

	"github.com/ruoyi-go/internal/model"
)

// DictService 字典服务接口
type DictService interface {
	// 字典类型查询
	GetDictTypeById(ctx context.Context, dictId uint64) (*model.SysDictType, error)
	GetDictTypeByType(ctx context.Context, dictType string) (*model.SysDictType, error)
	ListDictTypes(ctx context.Context, dictType *model.SysDictType, pageNum, pageSize int) ([]*model.SysDictType, int64, error)

	// 字典类型操作
	CreateDictType(ctx context.Context, dictType *model.SysDictType) error
	UpdateDictType(ctx context.Context, dictType *model.SysDictType) error
	DeleteDictTypeByIds(ctx context.Context, dictIds []uint64) error

	// 字典数据查询
	GetDictDataById(ctx context.Context, dictCode uint64) (*model.SysDictData, error)
	GetDictDataByTypeAndValue(ctx context.Context, dictType string, dictValue string) (*model.SysDictData, error)
	ListDictDataByType(ctx context.Context, dictType string) ([]*model.SysDictData, error)
	ListDictDatas(ctx context.Context, dictData *model.SysDictData, pageNum, pageSize int) ([]*model.SysDictData, int64, error)

	// 字典数据操作
	CreateDictData(ctx context.Context, dictData *model.SysDictData) error
	UpdateDictData(ctx context.Context, dictData *model.SysDictData) error
	DeleteDictDataByIds(ctx context.Context, dictCodes []uint64) error

	// 字典缓存
	RefreshCache(ctx context.Context) error
	GetDictLabel(ctx context.Context, dictType string, dictValue string) (string, error)

	// 字典校验
	CheckDictTypeUnique(ctx context.Context, dictId uint64, dictType string) (bool, error)
}
