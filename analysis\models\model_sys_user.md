# SysUser 模型分析

## 数据库表结构比较

### Java 版本 (MySQL)
```sql
create table sys_user (
  user_id           bigint(20)      not null auto_increment    comment '用户ID',
  dept_id           bigint(20)      default null               comment '部门ID',
  user_name         varchar(30)     not null                   comment '用户账号',
  nick_name         varchar(30)     not null                   comment '用户昵称',
  user_type         varchar(2)      default '00'               comment '用户类型（00系统用户）',
  email             varchar(50)     default ''                 comment '用户邮箱',
  phonenumber       varchar(11)     default ''                 comment '手机号码',
  sex               char(1)         default '0'                comment '用户性别（0男 1女 2未知）',
  avatar            varchar(100)    default ''                 comment '头像地址',
  password          varchar(100)    default ''                 comment '密码',
  status            char(1)         default '0'                comment '账号状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  login_ip          varchar(128)    default ''                 comment '最后登录IP',
  login_date        datetime                                   comment '最后登录时间',
  pwd_update_date   datetime                                   comment '密码最后更新时间',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id)
) engine=innodb auto_increment=100 comment = '用户信息表';
```

### Java 版本 (SQL Server)
```sql
CREATE TABLE [dbo].[sys_user] (
  [user_id] int  IDENTITY(101,1) NOT NULL,
  [dept_id] int DEFAULT NULL NULL,
  [login_name] nvarchar(30) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [user_name] nvarchar(30) COLLATE Chinese_PRC_CI_AS  NOT NULL,
  [user_type] nvarchar(2) COLLATE Chinese_PRC_CI_AS DEFAULT (N'00') NULL,
  [email] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [phonenumber] nvarchar(11) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [sex] nchar(1) COLLATE Chinese_PRC_CI_AS DEFAULT (N'0') NULL,
  [avatar] nvarchar(100) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [password] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [salt] nvarchar(20) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [status] nchar(1) COLLATE Chinese_PRC_CI_AS DEFAULT (N'0') NULL,
  [del_flag] nchar(1) COLLATE Chinese_PRC_CI_AS DEFAULT (N'0') NULL,
  [login_ip] nvarchar(50) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [login_date] datetime2(7) DEFAULT NULL NULL,
  [create_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [create_time] datetime2(7) DEFAULT NULL NULL,
  [update_by] nvarchar(64) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL,
  [update_time] datetime2(7) DEFAULT NULL NULL,
  [remark] nvarchar(500) COLLATE Chinese_PRC_CI_AS DEFAULT (N'') NULL
)
```

## 模型定义比较

### Java 版本 (SysUser.java)
```java
public class SysUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private Long deptId;

    /** 用户账号 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 用户邮箱 */
    private String email;

    /** 手机号码 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 账号状态（0正常 1停用） */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 部门对象 */
    private SysDept dept;

    /** 角色对象 */
    private List<SysRole> roles;

    /** 角色组 */
    private Long[] roleIds;

    /** 岗位组 */
    private Long[] postIds;
    
    /** 角色ID */
    private Long roleId;
}
```

### Go 版本 (sys_user.go)
```go
type SysUser struct {
    BaseEntity              // 包含基础字段
    UserId        uint64    `json:"userId" gorm:"column:user_id;primaryKey;comment:用户ID"`
    DeptId        uint64    `json:"deptId" gorm:"column:dept_id;comment:部门ID"`
    UserName      string    `json:"userName" gorm:"column:user_name;type:varchar(30);not null;comment:用户账号"`
    NickName      string    `json:"nickName" gorm:"column:nick_name;type:varchar(30);not null;comment:用户昵称"`
    UserType      string    `json:"userType" gorm:"column:user_type;type:varchar(2);default:00;comment:用户类型（00系统用户）"`
    Email         string    `json:"email" gorm:"column:email;type:varchar(50);comment:用户邮箱"`
    Phonenumber   string    `json:"phonenumber" gorm:"column:phonenumber;type:varchar(11);comment:手机号码"`
    Sex           string    `json:"sex" gorm:"column:sex;type:char(1);default:0;comment:用户性别（0男 1女 2未知）"`
    Avatar        string    `json:"avatar" gorm:"column:avatar;type:varchar(100);comment:头像地址"`
    Password      string    `json:"password" gorm:"column:password;type:varchar(100);comment:密码"`
    Salt          string    `json:"salt" gorm:"column:salt;type:varchar(20);comment:盐加密"`
    Status        string    `json:"status" gorm:"column:status;type:char(1);default:0;comment:帐号状态（0正常 1停用）"`
    LoginIp       string    `json:"loginIp" gorm:"column:login_ip;type:varchar(128);comment:最后登录IP"`
    LoginDate     time.Time `json:"loginDate" gorm:"column:login_date;comment:最后登录时间"`
    PwdUpdateDate time.Time `json:"pwdUpdateDate" gorm:"column:pwd_update_date;comment:密码最后更新时间"`

    // 关联关系
    Dept  *SysDept   `json:"dept" gorm:"foreignKey:DeptId;references:DeptId"`
    Roles []*SysRole `json:"roles" gorm:"-"` // 角色列表，不在数据库存储，通过关联表获取
    Posts []*SysPost `json:"posts" gorm:"-"` // 岗位列表，不在数据库存储，通过关联表获取

    // 非数据库字段
    RoleIds []uint64 `json:"roleIds" gorm:"-"` // 角色ID列表
    PostIds []uint64 `json:"postIds" gorm:"-"` // 岗位ID列表
    RoleId  uint64   `json:"roleId" gorm:"-"`  // 角色ID
}
```

## 主要区别

1. **数据类型差异**：
   - Java 版本使用 `Long` 作为 ID 类型，Go 版本使用 `uint64`
   - Java 版本使用 `Date` 作为时间类型，Go 版本使用 `time.Time`

2. **字段差异**：
   - Go 版本显式定义了 `UserType` 和 `Salt` 字段，这些字段在 Java 模型中没有直接定义，但在数据库表中存在
   - Java 版本将 `delFlag` 字段直接定义在 `SysUser` 类中，而 Go 版本通过继承 `BaseEntity` 获得该字段

3. **标记差异**：
   - Go 版本使用 GORM 标签定义列名、数据类型、约束等
   - Java 版本使用注解如 `@Excel` 标记字段属性

4. **关联关系处理**：
   - Go 版本通过 GORM 标签和指针类型定义关联关系
   - Java 版本直接使用对象引用

5. **ID 数组处理**：
   - Java 版本使用数组 `Long[]` 存储角色和岗位 ID 列表
   - Go 版本使用切片 `[]uint64` 存储角色和岗位 ID 列表 