package service

import (
	"context"

	"github.com/ruoyi-go/internal/model"
)

// ConfigService 参数配置服务接口
type ConfigService interface {
	// 配置查询
	GetConfigById(ctx context.Context, configId uint64) (*model.SysConfig, error)
	GetConfigByKey(ctx context.Context, configKey string) (*model.SysConfig, error)
	ListConfigs(ctx context.Context, config *model.SysConfig, pageNum, pageSize int) ([]*model.SysConfig, int64, error)

	// 配置操作
	CreateConfig(ctx context.Context, config *model.SysConfig) error
	UpdateConfig(ctx context.Context, config *model.SysConfig) error
	DeleteConfigByIds(ctx context.Context, configIds []uint64) error

	// 配置缓存
	RefreshCache(ctx context.Context) error
	GetConfigValueByKey(ctx context.Context, configKey string) (string, error)

	// 配置校验
	CheckConfigKeyUnique(ctx context.Context, configId uint64, configKey string) (bool, error)
}
