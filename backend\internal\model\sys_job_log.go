package model

import (
	"time"
)

// SysJobLog 定时任务调度日志表 sys_job_log
type SysJobLog struct {
	BaseModel
	JobLogId      uint64    `json:"jobLogId" gorm:"column:job_log_id;primaryKey;comment:任务日志ID"`
	JobName       string    `json:"jobName" gorm:"column:job_name;type:varchar(64);not null;comment:任务名称"`
	JobGroup      string    `json:"jobGroup" gorm:"column:job_group;type:varchar(64);not null;comment:任务组名"`
	InvokeTarget  string    `json:"invokeTarget" gorm:"column:invoke_target;type:varchar(500);not null;comment:调用目标字符串"`
	JobMessage    string    `json:"jobMessage" gorm:"column:job_message;type:varchar(500);comment:日志信息"`
	Status        string    `json:"status" gorm:"column:status;type:char(1);default:0;comment:执行状态（0正常 1失败）"`
	ExceptionInfo string    `json:"exceptionInfo" gorm:"column:exception_info;type:varchar(2000);comment:异常信息"`
	StartTime     time.Time `json:"startTime" gorm:"-;comment:开始时间"` // 非数据库字段，只用于API交互
	StopTime      time.Time `json:"stopTime" gorm:"-;comment:停止时间"`  // 非数据库字段，只用于API交互
}

// TableName 设置表名
func (SysJobLog) TableName() string {
	return "sys_job_log"
}
