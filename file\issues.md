# RuoYi框架Java到Go迁移问题跟踪

本文档用于记录RuoYi框架从Java迁移到Go过程中发现的问题、解决方案和决策。

## 问题记录格式

每个问题记录应包含以下信息：

- **问题ID**：唯一标识符，格式为"ISSUE-编号"
- **问题类型**：功能问题、兼容性问题、性能问题、安全问题等
- **问题描述**：详细描述问题的现象和影响
- **发现时间**：问题发现的时间
- **发现阶段**：分析阶段、设计阶段、实现阶段、测试阶段
- **严重程度**：阻塞、严重、中等、轻微
- **优先级**：高、中、低
- **状态**：未解决、解决中、已解决、已关闭
- **解决方案**：问题的解决方案或变通方法
- **解决时间**：问题解决的时间
- **相关模块**：与问题相关的模块
- **备注**：其他相关信息

## 问题列表

### ISSUE-001

- **问题ID**：ISSUE-001
- **问题类型**：待定
- **问题描述**：待定
- **发现时间**：待定
- **发现阶段**：待定
- **严重程度**：待定
- **优先级**：待定
- **状态**：未解决
- **解决方案**：待定
- **解决时间**：待定
- **相关模块**：待定
- **备注**：待定

## 决策记录

### DECISION-001

- **决策ID**：DECISION-001
- **决策类型**：待定
- **决策描述**：待定
- **决策时间**：待定
- **决策阶段**：待定
- **决策理由**：待定
- **替代方案**：待定
- **影响**：待定
- **相关模块**：待定
- **备注**：待定

## 风险记录

### RISK-001

- **风险ID**：RISK-001
- **风险类型**：待定
- **风险描述**：待定
- **识别时间**：待定
- **可能性**：高、中、低
- **影响程度**：高、中、低
- **风险等级**：高、中、低
- **缓解措施**：待定
- **状态**：未缓解、缓解中、已缓解
- **相关模块**：待定
- **备注**：待定

## 迁移过程中的问题跟踪

### 结构和设计问题

1. Go和Java语言的差异导致的设计模式转换问题
2. Spring Boot框架特性在Go中的替代实现
3. 前端Vue组件与Go后端的交互适配
4. 数据库ORM框架的差异（MyBatis vs GORM）

### 数据模型差异

1. 数据类型映射差异：
   - Java的`Long`对应Go的`uint64`
   - Java的`Integer`对应Go的`int`
   - Java的`Date`对应Go的`time.Time`
   - Java的`boolean`对应Go的`bool`

2. 基础实体设计差异：
   - Java版本的`BaseEntity`包含`searchValue`和`params`字段，Go版本需要添加
   - Go版本将`delFlag`统一放在`BaseEntity`中，Java版本在各实体中单独定义
   - 模型中的标记方式不同，Java使用注解，Go使用结构体标签

3. 用户模型特殊字段：
   - Go版本有`userType`和`salt`字段的显式定义，而Java版本的实体类中没有（但数据库中有）
   - Java版本在数据库中有`login_name`和`user_name`两个字段，Go版本只有`user_name`

4. 关联关系处理差异：
   - Java使用MyBatis的XML映射和服务层代码处理关联
   - Go使用GORM标签和显式关系定义
   - Java使用数组存储关联ID，Go使用切片

### 功能实现问题

1. 权限控制机制的重新实现
2. 数据权限过滤的实现
3. Excel导入导出功能
4. 代码生成器功能的迁移

### 性能问题

1. Go并发模型与Java多线程模型的性能差异
2. GORM与MyBatis的查询效率比较

### API兼容性问题

1. RESTful API路径和参数保持一致
2. 响应格式统一
3. 错误码和消息保持一致

### 解决方案和进展

#### 数据模型差异解决方案

1. ✅ 已在`base.go`中更新`BaseModel`，添加了`SearchValue`和`Params`字段
2. ✅ 统一使用`BaseEntity`包含`DelFlag`字段，确保所有模型继承基础实体
3. ✅ 在用户模型中保留`userType`和`salt`字段，与数据库保持一致

#### 待解决问题

1. 数据权限过滤机制的Go实现
2. 树形结构（部门、菜单）的处理方法
3. 导入导出功能的实现 