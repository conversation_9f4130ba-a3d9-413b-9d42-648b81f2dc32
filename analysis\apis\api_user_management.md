# 用户管理API接口分析

## 模块概述

用户管理模块是系统的核心模块，主要负责系统用户的增删改查、用户授权、密码重置、个人信息管理等功能。

## API接口列表

### 1. 用户列表查询

- **接口路径**：`/system/user/list`
- **请求方法**：GET
- **接口说明**：分页查询用户列表，支持多条件筛选
- **权限标识**：`system:user:list`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| pageNum | integer | 是 | 当前页码 |
| pageSize | integer | 是 | 每页记录数 |
| userName | string | 否 | 用户名 |
| phonenumber | string | 否 | 手机号码 |
| status | string | 否 | 状态（0正常 1停用） |
| deptId | integer | 否 | 部门ID |
| beginTime | string | 否 | 开始时间 |
| endTime | string | 否 | 结束时间 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "userId": 1,
        "deptId": 103,
        "userName": "admin",
        "nickName": "管理员",
        "email": "<EMAIL>",
        "phonenumber": "15888888888",
        "sex": "1",
        "avatar": "",
        "status": "0",
        "loginIp": "127.0.0.1",
        "loginDate": "2023-01-01 12:00:00",
        "dept": {
          "deptId": 103,
          "deptName": "研发部门",
          "leader": "张三"
        }
      }
    ]
  }
}
```

### 2. 获取用户详情

- **接口路径**：`/system/user/{userId}`
- **请求方法**：GET
- **接口说明**：根据用户ID查询用户详细信息
- **权限标识**：`system:user:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1,
    "deptId": 103,
    "userName": "admin",
    "nickName": "管理员",
    "email": "<EMAIL>",
    "phonenumber": "15888888888",
    "sex": "1",
    "avatar": "",
    "status": "0",
    "roleIds": [1, 2],
    "postIds": [1, 2],
    "dept": {
      "deptId": 103,
      "deptName": "研发部门",
      "leader": "张三"
    },
    "roles": [
      {
        "roleId": 1,
        "roleName": "超级管理员",
        "roleKey": "admin"
      }
    ],
    "posts": [
      {
        "postId": 1,
        "postName": "董事长"
      }
    ]
  }
}
```

### 3. 新增用户

- **接口路径**：`/system/user`
- **请求方法**：POST
- **接口说明**：新增用户信息
- **权限标识**：`system:user:add`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| deptId | integer | 是 | 部门ID |
| userName | string | 是 | 用户名 |
| nickName | string | 是 | 用户昵称 |
| password | string | 是 | 密码 |
| email | string | 否 | 邮箱 |
| phonenumber | string | 否 | 手机号码 |
| sex | string | 否 | 用户性别（0男 1女 2未知） |
| status | string | 否 | 状态（0正常 1停用） |
| roleIds | array | 是 | 角色ID数组 |
| postIds | array | 是 | 岗位ID数组 |
| remark | string | 否 | 备注 |

- **请求示例**：
```json
{
  "deptId": 103,
  "userName": "test",
  "nickName": "测试用户",
  "password": "123456",
  "email": "<EMAIL>",
  "phonenumber": "15888888889",
  "sex": "0",
  "status": "0",
  "roleIds": [2],
  "postIds": [4],
  "remark": "测试账号"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "新增成功"
}
```

### 4. 修改用户

- **接口路径**：`/system/user`
- **请求方法**：PUT
- **接口说明**：修改用户信息
- **权限标识**：`system:user:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |
| deptId | integer | 是 | 部门ID |
| userName | string | 是 | 用户名 |
| nickName | string | 是 | 用户昵称 |
| email | string | 否 | 邮箱 |
| phonenumber | string | 否 | 手机号码 |
| sex | string | 否 | 用户性别（0男 1女 2未知） |
| status | string | 否 | 状态（0正常 1停用） |
| roleIds | array | 是 | 角色ID数组 |
| postIds | array | 是 | 岗位ID数组 |
| remark | string | 否 | 备注 |

- **请求示例**：
```json
{
  "userId": 2,
  "deptId": 105,
  "userName": "test",
  "nickName": "测试用户修改",
  "email": "<EMAIL>",
  "phonenumber": "15888888899",
  "sex": "1",
  "status": "0",
  "roleIds": [2, 3],
  "postIds": [3],
  "remark": "测试账号已修改"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 5. 删除用户

- **接口路径**：`/system/user/{userIds}`
- **请求方法**：DELETE
- **接口说明**：删除用户信息
- **权限标识**：`system:user:remove`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userIds | string | 是 | 用户ID串，多个用逗号分隔 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "删除成功"
}
```

### 6. 重置密码

- **接口路径**：`/system/user/resetPwd`
- **请求方法**：PUT
- **接口说明**：重置用户密码
- **权限标识**：`system:user:resetPwd`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |
| password | string | 是 | 新密码 |

- **请求示例**：
```json
{
  "userId": 2,
  "password": "123456"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "密码重置成功"
}
```

### 7. 更改用户状态

- **接口路径**：`/system/user/changeStatus`
- **请求方法**：PUT
- **接口说明**：修改用户状态
- **权限标识**：`system:user:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |
| status | string | 是 | 状态（0正常 1停用） |

- **请求示例**：
```json
{
  "userId": 2,
  "status": "1"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "状态修改成功"
}
```

### 8. 获取用户授权角色

- **接口路径**：`/system/user/authRole/{userId}`
- **请求方法**：GET
- **接口说明**：获取已分配角色的用户列表
- **权限标识**：`system:user:query`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "user": {
      "userId": 2,
      "userName": "test",
      "nickName": "测试用户"
    },
    "roles": [
      {
        "roleId": 2,
        "roleName": "普通角色",
        "roleKey": "common",
        "flag": true
      },
      {
        "roleId": 3,
        "roleName": "测试角色",
        "roleKey": "test",
        "flag": false
      }
    ]
  }
}
```

### 9. 保存用户授权角色

- **接口路径**：`/system/user/authRole`
- **请求方法**：PUT
- **接口说明**：保存用户分配角色
- **权限标识**：`system:user:edit`
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userId | integer | 是 | 用户ID |
| roleIds | string | 是 | 角色ID串，多个用逗号分隔 |

- **请求示例**：
```json
{
  "userId": 2,
  "roleIds": "2,3"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "授权成功"
}
```

### 10. 获取个人信息

- **接口路径**：`/system/user/profile`
- **请求方法**：GET
- **接口说明**：获取当前登录用户信息
- **权限标识**：无（所有已登录用户）
- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "user": {
      "userId": 1,
      "userName": "admin",
      "nickName": "管理员",
      "email": "<EMAIL>",
      "phonenumber": "15888888888",
      "sex": "1",
      "avatar": ""
    },
    "roleGroup": "超级管理员",
    "postGroup": "董事长,总经理"
  }
}
```

### 11. 修改个人信息

- **接口路径**：`/system/user/profile`
- **请求方法**：PUT
- **接口说明**：修改当前登录用户信息
- **权限标识**：无（所有已登录用户）
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| nickName | string | 是 | 用户昵称 |
| email | string | 否 | 邮箱 |
| phonenumber | string | 否 | 手机号码 |
| sex | string | 否 | 用户性别（0男 1女 2未知） |

- **请求示例**：
```json
{
  "nickName": "管理员",
  "email": "<EMAIL>",
  "phonenumber": "15888888880",
  "sex": "0"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 12. 修改个人密码

- **接口路径**：`/system/user/profile/updatePwd`
- **请求方法**：PUT
- **接口说明**：修改当前登录用户密码
- **权限标识**：无（所有已登录用户）
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| oldPassword | string | 是 | 旧密码 |
| newPassword | string | 是 | 新密码 |

- **请求示例**：
```json
{
  "oldPassword": "123456",
  "newPassword": "abcdef"
}
```

- **响应示例**：
```json
{
  "code": 200,
  "msg": "修改成功"
}
```

### 13. 上传头像

- **接口路径**：`/system/user/profile/avatar`
- **请求方法**：POST
- **接口说明**：上传用户头像
- **权限标识**：无（所有已登录用户）
- **请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| avatarfile | file | 是 | 头像文件 |

- **响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "imgUrl": "/profile/avatar/2023/01/01/xxx.jpg"
}
```

## 接口实现注意事项

1. **数据权限控制**：
   - 用户列表查询需要根据当前用户的数据权限进行过滤
   - 管理员可以查看所有用户，普通用户只能查看指定数据范围内的用户

2. **密码处理**：
   - 密码存储时需要加密，不能明文存储
   - 密码重置和修改时需要验证密码复杂度

3. **用户状态**：
   - 停用的用户无法登录系统
   - 不能停用当前登录的用户

4. **特殊用户保护**：
   - 管理员账号不能被删除
   - 不能删除当前登录的用户

5. **角色和岗位处理**：
   - 用户的增删改需要同时处理用户与角色、岗位的关联关系
   - 授权角色时需要先清除原有关联关系，再建立新的关联关系

6. **用户名唯一性**：
   - 新增用户时需要验证用户名的唯一性
   - 修改用户时，如果修改了用户名，也需要验证唯一性

7. **邮箱和手机号验证**：
   - 邮箱和手机号需要验证格式的正确性
   - 可以考虑验证邮箱和手机号的唯一性

## Gin+GORM实现建议

1. **Controller层**：
```go
// UserController 处理用户相关请求
type UserController struct {
    UserService service.IUserService
    RoleService service.IRoleService
    PostService service.IPostService
}

// List 获取用户列表
func (c *UserController) List(ctx *gin.Context) {
    // 解析查询参数
    var req request.UserListRequest
    if err := ctx.ShouldBindQuery(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 调用Service层获取数据
    list, total, err := c.UserService.SelectUserList(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "查询失败")
        return
    }
    
    // 返回结果
    response.Success(ctx, gin.H{
        "rows": list,
        "total": total,
    })
}

// GetInfo 获取用户详情
func (c *UserController) GetInfo(ctx *gin.Context) {
    userId := ctx.Param("userId")
    // ...
}

// Add 添加用户
func (c *UserController) Add(ctx *gin.Context) {
    var req request.UserAddRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.Error(ctx, http.StatusBadRequest, "参数错误")
        return
    }
    
    // 检查用户名唯一性
    if c.UserService.CheckUserNameUnique(ctx, req.UserName) {
        response.Error(ctx, http.StatusBadRequest, "用户名已存在")
        return
    }
    
    // 创建用户
    err := c.UserService.InsertUser(ctx, req)
    if err != nil {
        response.Error(ctx, http.StatusInternalServerError, "新增用户失败")
        return
    }
    
    response.Success(ctx, nil)
}

// 其他方法...
```

2. **Service层**：
```go
// IUserService 用户服务接口
type IUserService interface {
    SelectUserList(ctx *gin.Context, req request.UserListRequest) ([]*models.SysUser, int64, error)
    SelectUserById(ctx *gin.Context, userId uint64) (*models.SysUser, error)
    InsertUser(ctx *gin.Context, req request.UserAddRequest) error
    UpdateUser(ctx *gin.Context, req request.UserUpdateRequest) error
    DeleteUserByIds(ctx *gin.Context, userIds []uint64) error
    ResetPassword(ctx *gin.Context, userId uint64, password string) error
    ChangeStatus(ctx *gin.Context, userId uint64, status string) error
    SelectAllocatedRoleList(ctx *gin.Context, userId uint64) ([]*models.SysRole, error)
    InsertUserAuth(ctx *gin.Context, userId uint64, roleIds []uint64) error
    CheckUserNameUnique(ctx *gin.Context, userName string) bool
    // 其他方法...
}

// UserService 用户服务实现
type UserService struct {
    DB *gorm.DB
}

// SelectUserList 查询用户列表
func (s *UserService) SelectUserList(ctx *gin.Context, req request.UserListRequest) ([]*models.SysUser, int64, error) {
    var users []*models.SysUser
    var total int64
    
    db := s.DB.Model(&models.SysUser{}).Where("del_flag = ?", "0")
    
    // 添加查询条件
    if req.UserName != "" {
        db = db.Where("user_name like ?", "%"+req.UserName+"%")
    }
    
    if req.Phonenumber != "" {
        db = db.Where("phonenumber like ?", "%"+req.Phonenumber+"%")
    }
    
    if req.Status != "" {
        db = db.Where("status = ?", req.Status)
    }
    
    if req.DeptId > 0 {
        // 查询部门及其子部门
        deptIds := s.getDeptAndChildrenIds(req.DeptId)
        db = db.Where("dept_id in ?", deptIds)
    }
    
    // 数据权限过滤
    db = s.dataScope(ctx, db)
    
    // 统计总数
    db.Count(&total)
    
    // 分页查询
    if err := db.Preload("Dept").Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).Find(&users).Error; err != nil {
        return nil, 0, err
    }
    
    return users, total, nil
}

// 其他方法...

// dataScope 数据权限过滤
func (s *UserService) dataScope(ctx *gin.Context, db *gorm.DB) *gorm.DB {
    // 获取当前用户
    currentUser := auth.GetCurrentUser(ctx)
    
    // 如果是管理员，不进行数据过滤
    if currentUser.IsAdmin() {
        return db
    }
    
    // 获取用户的角色数据权限范围
    dataScope := s.getUserDataScope(currentUser.UserId)
    
    // 根据数据范围类型进行过滤
    switch dataScope {
    case "1": // 全部数据权限
        return db
    case "2": // 自定数据权限
        return db.Where("dept_id in (?)", s.getRoleDeptIds(currentUser.UserId))
    case "3": // 本部门数据权限
        return db.Where("dept_id = ?", currentUser.DeptId)
    case "4": // 本部门及以下数据权限
        return db.Where("dept_id in (?)", s.getDeptAndChildrenIds(currentUser.DeptId))
    case "5": // 仅本人数据权限
        return db.Where("create_by = ?", currentUser.UserName)
    default:
        return db.Where("create_by = ?", currentUser.UserName)
    }
}
```

3. **Model层**：
```go
// 已在前面定义了SysUser模型
```

4. **请求和响应结构**：
```go
// UserListRequest 用户列表请求
type UserListRequest struct {
    PageNum     int    `form:"pageNum"`
    PageSize    int    `form:"pageSize"`
    UserName    string `form:"userName"`
    Phonenumber string `form:"phonenumber"`
    Status      string `form:"status"`
    DeptId      uint64 `form:"deptId"`
    BeginTime   string `form:"beginTime"`
    EndTime     string `form:"endTime"`
}

// UserAddRequest 新增用户请求
type UserAddRequest struct {
    DeptId      uint64   `json:"deptId" binding:"required"`
    UserName    string   `json:"userName" binding:"required"`
    NickName    string   `json:"nickName" binding:"required"`
    Password    string   `json:"password" binding:"required"`
    Email       string   `json:"email"`
    Phonenumber string   `json:"phonenumber"`
    Sex         string   `json:"sex"`
    Status      string   `json:"status"`
    RoleIds     []uint64 `json:"roleIds" binding:"required"`
    PostIds     []uint64 `json:"postIds" binding:"required"`
    Remark      string   `json:"remark"`
}

// 其他请求结构...
```

5. **路由注册**：
```go
// 注册用户管理路由
func RegisterUserRoutes(r *gin.RouterGroup, userController *controller.UserController) {
    userRouter := r.Group("/system/user")
    {
        userRouter.GET("/list", middleware.Auth(), middleware.HasPermission("system:user:list"), userController.List)
        userRouter.GET("/:userId", middleware.Auth(), middleware.HasPermission("system:user:query"), userController.GetInfo)
        userRouter.POST("", middleware.Auth(), middleware.HasPermission("system:user:add"), userController.Add)
        userRouter.PUT("", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.Update)
        userRouter.DELETE("/:userIds", middleware.Auth(), middleware.HasPermission("system:user:remove"), userController.Delete)
        userRouter.PUT("/resetPwd", middleware.Auth(), middleware.HasPermission("system:user:resetPwd"), userController.ResetPwd)
        userRouter.PUT("/changeStatus", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.ChangeStatus)
        userRouter.GET("/authRole/:userId", middleware.Auth(), middleware.HasPermission("system:user:query"), userController.AuthRole)
        userRouter.PUT("/authRole", middleware.Auth(), middleware.HasPermission("system:user:edit"), userController.InsertAuthRole)
        
        // 个人信息相关
        profileRouter := userRouter.Group("/profile")
        {
            profileRouter.GET("", middleware.Auth(), userController.Profile)
            profileRouter.PUT("", middleware.Auth(), userController.UpdateProfile)
            profileRouter.PUT("/updatePwd", middleware.Auth(), userController.UpdatePwd)
            profileRouter.POST("/avatar", middleware.Auth(), userController.UploadAvatar)
        }
    }
} 