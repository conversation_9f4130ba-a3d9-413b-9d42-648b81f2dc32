package model

// SysMenu 菜单表 sys_menu
type SysMenu struct {
	BaseEntity
	MenuId    uint64 `json:"menuId" gorm:"column:menu_id;primaryKey;comment:菜单ID"`
	MenuName  string `json:"menuName" gorm:"column:menu_name;type:varchar(50);not null;comment:菜单名称"`
	ParentId  uint64 `json:"parentId" gorm:"column:parent_id;default:0;comment:父菜单ID"`
	OrderNum  int    `json:"orderNum" gorm:"column:order_num;default:0;comment:显示顺序"`
	Path      string `json:"path" gorm:"column:path;type:varchar(200);default:'';comment:路由地址"`
	Component string `json:"component" gorm:"column:component;type:varchar(255);comment:组件路径"`
	Query     string `json:"query" gorm:"column:query;type:varchar(255);comment:路由参数"`
	IsFrame   string `json:"isFrame" gorm:"column:is_frame;type:char(1);default:1;comment:是否为外链（0是 1否）"`
	IsCache   string `json:"isCache" gorm:"column:is_cache;type:char(1);default:0;comment:是否缓存（0缓存 1不缓存）"`
	MenuType  string `json:"menuType" gorm:"column:menu_type;type:char(1);default:'';comment:菜单类型（M目录 C菜单 F按钮）"`
	Visible   string `json:"visible" gorm:"column:visible;type:char(1);default:0;comment:菜单状态（0显示 1隐藏）"`
	Status    string `json:"status" gorm:"column:status;type:char(1);default:0;comment:菜单状态（0正常 1停用）"`
	Perms     string `json:"perms" gorm:"column:perms;type:varchar(100);comment:权限标识"`
	Icon      string `json:"icon" gorm:"column:icon;type:varchar(100);default:#;comment:菜单图标"`
	RouteName string `json:"routeName" gorm:"column:route_name;type:varchar(128);comment:路由名称"`
	Target    string `json:"target" gorm:"column:target;type:varchar(20);comment:打开方式（menuItem页签 menuBlank新窗口）"`

	// 非数据库字段
	ParentName string     `json:"parentName" gorm:"-"` // 父菜单名称
	Children   []*SysMenu `json:"children" gorm:"-"`   // 子菜单
}

// TableName 设置表名
func (SysMenu) TableName() string {
	return "sys_menu"
}
