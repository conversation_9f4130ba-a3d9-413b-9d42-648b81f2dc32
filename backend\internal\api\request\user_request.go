package request

// UserListRequest 用户列表请求参数
type UserListRequest struct {
	PageNum     int    `form:"pageNum" binding:"required,min=1"`
	PageSize    int    `form:"pageSize" binding:"required,min=1,max=100"`
	UserName    string `form:"userName"`
	Status      string `form:"status"`
	Phonenumber string `form:"phonenumber"`
	DeptId      uint64 `form:"deptId"`
	BeginTime   string `form:"beginTime"`
	EndTime     string `form:"endTime"`
}

// UserAddRequest 添加用户请求参数
type UserAddRequest struct {
	UserName    string   `json:"userName" binding:"required,min=2,max=20"`
	NickName    string   `json:"nickName" binding:"required,min=2,max=30"`
	Password    string   `json:"password" binding:"required,min=6,max=20"`
	DeptId      uint64   `json:"deptId" binding:"required"`
	Phonenumber string   `json:"phonenumber" binding:"omitempty,len=11"`
	Email       string   `json:"email" binding:"omitempty,email"`
	Sex         string   `json:"sex" binding:"required,oneof=0 1 2"`
	Status      string   `json:"status" binding:"required,oneof=0 1"`
	RoleIds     []uint64 `json:"roleIds" binding:"required"`
	PostIds     []uint64 `json:"postIds" binding:"required"`
	Remark      string   `json:"remark"`
}

// UserUpdateRequest 修改用户请求参数
type UserUpdateRequest struct {
	UserId      uint64   `json:"userId" binding:"required"`
	UserName    string   `json:"userName" binding:"required,min=2,max=20"`
	NickName    string   `json:"nickName" binding:"required,min=2,max=30"`
	DeptId      uint64   `json:"deptId" binding:"required"`
	Phonenumber string   `json:"phonenumber" binding:"omitempty,len=11"`
	Email       string   `json:"email" binding:"omitempty,email"`
	Sex         string   `json:"sex" binding:"required,oneof=0 1 2"`
	Status      string   `json:"status" binding:"required,oneof=0 1"`
	RoleIds     []uint64 `json:"roleIds" binding:"required"`
	PostIds     []uint64 `json:"postIds" binding:"required"`
	Remark      string   `json:"remark"`
}

// ResetPwdRequest 重置密码请求参数
type ResetPwdRequest struct {
	UserId   uint64 `json:"userId" binding:"required"`
	Password string `json:"password" binding:"required,min=6,max=20"`
}

// ChangeStatusRequest 修改用户状态请求参数
type ChangeStatusRequest struct {
	UserId uint64 `json:"userId" binding:"required"`
	Status string `json:"status" binding:"required,oneof=0 1"`
}

// AuthRoleRequest 授权用户角色请求参数
type AuthRoleRequest struct {
	UserId  uint64 `json:"userId" binding:"required"`
	RoleIds string `json:"roleIds" binding:"required"`
}

// UpdateProfileRequest 修改个人信息请求参数
type UpdateProfileRequest struct {
	NickName    string `json:"nickName" binding:"required,min=2,max=30"`
	Phonenumber string `json:"phonenumber" binding:"omitempty,len=11"`
	Email       string `json:"email" binding:"omitempty,email"`
	Sex         string `json:"sex" binding:"required,oneof=0 1 2"`
}

// UpdatePwdRequest 修改密码请求参数
type UpdatePwdRequest struct {
	OldPassword string `json:"oldPassword" binding:"required,min=6,max=20"`
	NewPassword string `json:"newPassword" binding:"required,min=6,max=20"`
}
