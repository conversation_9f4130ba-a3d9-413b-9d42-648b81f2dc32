package service

import (
	"context"
	"errors"
	"time"

	"github.com/ruoyi-go/internal/middleware"
	"github.com/ruoyi-go/internal/model"
	"gorm.io/gorm"
)

// RoleService 角色服务接口
type RoleService interface {
	// 基础查询
	GetRoleById(ctx context.Context, roleId uint64) (*model.SysRole, error)
	ListRoles(ctx context.Context, role *model.SysRole, pageNum, pageSize int) ([]*model.SysRole, int64, error)

	// 角色操作
	CreateRole(ctx context.Context, role *model.SysRole) error
	UpdateRole(ctx context.Context, role *model.SysRole) error
	DeleteRoleByIds(ctx context.Context, roleIds []uint64) error
	UpdateRoleStatus(ctx context.Context, roleId uint64, status string) error

	// 角色数据权限
	AuthDataScope(ctx context.Context, roleId uint64, deptIds []uint64, dataScope string) error

	// 角色菜单关系
	GetRoleMenuIds(ctx context.Context, roleId uint64) ([]uint64, error)

	// 角色部门关系
	GetRoleDeptIds(ctx context.Context, roleId uint64) ([]uint64, error)

	// 角色分配
	GetAllocatedUserList(ctx context.Context, roleId uint64, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error)
	GetUnallocatedUserList(ctx context.Context, roleId uint64, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error)

	// 角色用户分配操作
	AddRoleUsers(ctx context.Context, roleId uint64, userIds []uint64) error
	DeleteRoleUser(ctx context.Context, roleId uint64, userId uint64) error
	DeleteRoleUsers(ctx context.Context, roleId uint64, userIds []uint64) error

	// 校验
	CheckRoleNameUnique(ctx context.Context, roleId uint64, roleName string) (bool, error)
	CheckRoleKeyUnique(ctx context.Context, roleId uint64, roleKey string) (bool, error)
	CheckRoleAllowed(roleId uint64) error
}

// roleServiceImpl 角色服务实现
type roleServiceImpl struct {
	db *gorm.DB
}

// NewRoleService 创建角色服务
func NewRoleService(db *gorm.DB) RoleService {
	return &roleServiceImpl{
		db: db,
	}
}

// GetRoleById 根据角色ID获取角色信息
func (s *roleServiceImpl) GetRoleById(ctx context.Context, roleId uint64) (*model.SysRole, error) {
	var role model.SysRole
	err := s.db.Where("role_id = ? AND del_flag = '0'", roleId).First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

// ListRoles 查询角色列表，实现数据权限过滤
func (s *roleServiceImpl) ListRoles(ctx context.Context, role *model.SysRole, pageNum, pageSize int) ([]*model.SysRole, int64, error) {
	var roles []*model.SysRole
	var total int64

	// 初始化查询条件
	query := s.db.Model(&model.SysRole{}).Where("del_flag = '0'")

	// 添加查询条件
	if role != nil {
		if role.RoleName != "" {
			query = query.Where("role_name like ?", "%"+role.RoleName+"%")
		}
		if role.Status != "" {
			query = query.Where("status = ?", role.Status)
		}
		if role.RoleKey != "" {
			query = query.Where("role_key like ?", "%"+role.RoleKey+"%")
		}

		// 创建一个基础实体来接收数据权限过滤条件
		// 与Java版本一致，使用d作为部门别名
		baseEntity := &model.BaseEntity{}
		if baseEntity.Params == nil {
			baseEntity.Params = make(map[string]interface{})
		}

		// 获取当前登录用户，从上下文中获取
		// 在实际实现中，需要从上下文中获取用户信息
		// 这里简化处理，未实现上下文用户获取
		user := getUserFromContext(ctx)
		if user != nil {
			// 调用数据权限过滤
			middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)

			// 应用数据权限过滤
			if baseEntity.Params != nil {
				if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
					// 拼接数据权限SQL，需要关联sys_role_dept表和sys_dept表
					query = query.Joins("LEFT JOIN sys_role_dept rd ON rd.role_id = sys_role.role_id")
					query = query.Joins("LEFT JOIN sys_dept d ON d.dept_id = rd.dept_id")
					query = query.Where(dataScope)
					// 去重
					query = query.Group("sys_role.role_id")
				}
			}
		}
	}

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 排序
	query = query.Order("role_sort")

	// 查询角色列表
	err := query.Find(&roles).Error
	if err != nil {
		return nil, 0, err
	}

	return roles, total, nil
}

// CreateRole 创建角色
func (s *roleServiceImpl) CreateRole(ctx context.Context, role *model.SysRole) error {
	// 检查角色名称唯一性
	isUnique, err := s.CheckRoleNameUnique(ctx, 0, role.RoleName)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("角色名称已存在")
	}

	// 检查权限字符唯一性
	isUnique, err = s.CheckRoleKeyUnique(ctx, 0, role.RoleKey)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("权限字符已存在")
	}

	// 设置创建时间
	role.CreateTime = time.Now()

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 保存角色信息
		if err := tx.Create(role).Error; err != nil {
			return err
		}

		// 保存角色菜单关系
		if len(role.MenuIds) > 0 {
			roleMenus := make([]model.SysRoleMenu, 0, len(role.MenuIds))
			for _, menuId := range role.MenuIds {
				roleMenus = append(roleMenus, model.SysRoleMenu{
					RoleId: role.RoleId,
					MenuId: menuId,
				})
			}
			if err := tx.Create(&roleMenus).Error; err != nil {
				return err
			}
		}

		// 如果是自定义数据权限，保存角色部门关系
		if role.DataScope == "2" && len(role.DeptIds) > 0 {
			roleDepts := make([]model.SysRoleDept, 0, len(role.DeptIds))
			for _, deptId := range role.DeptIds {
				roleDepts = append(roleDepts, model.SysRoleDept{
					RoleId: role.RoleId,
					DeptId: deptId,
				})
			}
			if err := tx.Create(&roleDepts).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// AuthDataScope 修改角色数据权限
func (s *roleServiceImpl) AuthDataScope(ctx context.Context, roleId uint64, deptIds []uint64, dataScope string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 修改角色数据范围
		if err := tx.Model(&model.SysRole{}).Where("role_id = ?", roleId).
			Update("data_scope", dataScope).Error; err != nil {
			return err
		}

		// 如果是自定义数据权限，更新角色部门关系
		if dataScope == "2" {
			// 删除原有角色部门关系
			if err := tx.Where("role_id = ?", roleId).Delete(&model.SysRoleDept{}).Error; err != nil {
				return err
			}

			// 添加新的角色部门关系
			if len(deptIds) > 0 {
				roleDepts := make([]model.SysRoleDept, 0, len(deptIds))
				for _, deptId := range deptIds {
					roleDepts = append(roleDepts, model.SysRoleDept{
						RoleId: roleId,
						DeptId: deptId,
					})
				}
				if err := tx.Create(&roleDepts).Error; err != nil {
					return err
				}
			}
		} else {
			// 不是自定义数据权限，删除原有角色部门关系
			if err := tx.Where("role_id = ?", roleId).Delete(&model.SysRoleDept{}).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// UpdateRole 修改角色
func (s *roleServiceImpl) UpdateRole(ctx context.Context, role *model.SysRole) error {
	// 检查角色名称唯一性
	isUnique, err := s.CheckRoleNameUnique(ctx, role.RoleId, role.RoleName)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("角色名称已存在")
	}

	// 检查权限字符唯一性
	isUnique, err = s.CheckRoleKeyUnique(ctx, role.RoleId, role.RoleKey)
	if err != nil {
		return err
	}
	if !isUnique {
		return errors.New("权限字符已存在")
	}

	// 设置修改时间
	role.UpdateTime = time.Now()

	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新角色信息
		if err := tx.Model(&model.SysRole{}).Where("role_id = ?", role.RoleId).
			Omit("create_time", "del_flag").Updates(role).Error; err != nil {
			return err
		}

		// 删除原有角色菜单关系
		if err := tx.Where("role_id = ?", role.RoleId).Delete(&model.SysRoleMenu{}).Error; err != nil {
			return err
		}

		// 添加新的角色菜单关系
		if len(role.MenuIds) > 0 {
			roleMenus := make([]model.SysRoleMenu, 0, len(role.MenuIds))
			for _, menuId := range role.MenuIds {
				roleMenus = append(roleMenus, model.SysRoleMenu{
					RoleId: role.RoleId,
					MenuId: menuId,
				})
			}
			if err := tx.Create(&roleMenus).Error; err != nil {
				return err
			}
		}

		// 如果是自定义数据权限，更新角色部门关系
		if role.DataScope == "2" {
			// 删除原有角色部门关系
			if err := tx.Where("role_id = ?", role.RoleId).Delete(&model.SysRoleDept{}).Error; err != nil {
				return err
			}

			// 添加新的角色部门关系
			if len(role.DeptIds) > 0 {
				roleDepts := make([]model.SysRoleDept, 0, len(role.DeptIds))
				for _, deptId := range role.DeptIds {
					roleDepts = append(roleDepts, model.SysRoleDept{
						RoleId: role.RoleId,
						DeptId: deptId,
					})
				}
				if err := tx.Create(&roleDepts).Error; err != nil {
					return err
				}
			}
		} else {
			// 不是自定义数据权限，删除原有角色部门关系
			if err := tx.Where("role_id = ?", role.RoleId).Delete(&model.SysRoleDept{}).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// DeleteRoleByIds 批量删除角色
func (s *roleServiceImpl) DeleteRoleByIds(ctx context.Context, roleIds []uint64) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除角色信息
		if err := tx.Model(&model.SysRole{}).Where("role_id IN ?", roleIds).
			Update("del_flag", "2").Error; err != nil {
			return err
		}

		// 删除角色菜单关系
		if err := tx.Where("role_id IN ?", roleIds).Delete(&model.SysRoleMenu{}).Error; err != nil {
			return err
		}

		// 删除角色部门关系
		if err := tx.Where("role_id IN ?", roleIds).Delete(&model.SysRoleDept{}).Error; err != nil {
			return err
		}

		// 删除角色用户关系
		if err := tx.Where("role_id IN ?", roleIds).Delete(&model.SysUserRole{}).Error; err != nil {
			return err
		}

		return nil
	})
}

// UpdateRoleStatus 修改角色状态
func (s *roleServiceImpl) UpdateRoleStatus(ctx context.Context, roleId uint64, status string) error {
	return s.db.Model(&model.SysRole{}).Where("role_id = ?", roleId).
		Update("status", status).Error
}

// GetRoleMenuIds 获取角色菜单ID列表
func (s *roleServiceImpl) GetRoleMenuIds(ctx context.Context, roleId uint64) ([]uint64, error) {
	var menuIds []uint64
	err := s.db.Model(&model.SysRoleMenu{}).
		Where("role_id = ?", roleId).
		Pluck("menu_id", &menuIds).Error
	return menuIds, err
}

// GetRoleDeptIds 获取角色部门ID列表
func (s *roleServiceImpl) GetRoleDeptIds(ctx context.Context, roleId uint64) ([]uint64, error) {
	var deptIds []uint64
	err := s.db.Model(&model.SysRoleDept{}).
		Where("role_id = ?", roleId).
		Pluck("dept_id", &deptIds).Error
	return deptIds, err
}

// GetAllocatedUserList 获取已分配用户角色列表
func (s *roleServiceImpl) GetAllocatedUserList(ctx context.Context, roleId uint64, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error) {
	var users []*model.SysUser
	var total int64

	// 使用子查询方式查询已分配用户列表
	query := s.db.Model(&model.SysUser{}).
		Joins("INNER JOIN sys_user_role ur ON ur.user_id = sys_user.user_id AND ur.role_id = ?", roleId).
		Where("sys_user.del_flag = '0'")

	// 添加查询条件
	if user != nil {
		if user.UserName != "" {
			query = query.Where("sys_user.user_name like ?", "%"+user.UserName+"%")
		}
		if user.Status != "" {
			query = query.Where("sys_user.status = ?", user.Status)
		}
		if user.Phonenumber != "" {
			query = query.Where("sys_user.phonenumber like ?", "%"+user.Phonenumber+"%")
		}

		// 应用数据权限过滤
		baseEntity := &model.BaseEntity{}
		if baseEntity.Params == nil {
			baseEntity.Params = make(map[string]interface{})
		}

		// 获取当前登录用户
		loginUser := getUserFromContext(ctx)
		if loginUser != nil {
			// 调用数据权限过滤
			middleware.DataScopeFilter(ctx, loginUser, "d", "", "", baseEntity)

			// 应用数据权限过滤
			if baseEntity.Params != nil {
				if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
					// 拼接数据权限SQL，需要关联sys_dept表
					query = query.Joins("LEFT JOIN sys_dept d ON d.dept_id = sys_user.dept_id")
					query = query.Where(dataScope)
				}
			}
		}
	}

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 查询用户列表
	err := query.Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// GetUnallocatedUserList 获取未分配用户角色列表
func (s *roleServiceImpl) GetUnallocatedUserList(ctx context.Context, roleId uint64, user *model.SysUser, pageNum, pageSize int) ([]*model.SysUser, int64, error) {
	var users []*model.SysUser
	var total int64

	// 使用子查询方式查询未分配用户列表
	query := s.db.Model(&model.SysUser{}).
		Where("sys_user.del_flag = '0'").
		Where("sys_user.user_id NOT IN (SELECT user_id FROM sys_user_role WHERE role_id = ?)", roleId)

	// 添加查询条件
	if user != nil {
		if user.UserName != "" {
			query = query.Where("sys_user.user_name like ?", "%"+user.UserName+"%")
		}
		if user.Status != "" {
			query = query.Where("sys_user.status = ?", user.Status)
		}
		if user.Phonenumber != "" {
			query = query.Where("sys_user.phonenumber like ?", "%"+user.Phonenumber+"%")
		}

		// 应用数据权限过滤
		baseEntity := &model.BaseEntity{}
		if baseEntity.Params == nil {
			baseEntity.Params = make(map[string]interface{})
		}

		// 获取当前登录用户
		loginUser := getUserFromContext(ctx)
		if loginUser != nil {
			// 调用数据权限过滤
			middleware.DataScopeFilter(ctx, loginUser, "d", "", "", baseEntity)

			// 应用数据权限过滤
			if baseEntity.Params != nil {
				if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
					// 拼接数据权限SQL，需要关联sys_dept表
					query = query.Joins("LEFT JOIN sys_dept d ON d.dept_id = sys_user.dept_id")
					query = query.Where(dataScope)
				}
			}
		}
	}

	// 查询总数
	query.Count(&total)

	// 分页查询
	if pageNum > 0 && pageSize > 0 {
		offset := (pageNum - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 查询用户列表
	err := query.Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// AddRoleUsers 批量新增用户角色关系
func (s *roleServiceImpl) AddRoleUsers(ctx context.Context, roleId uint64, userIds []uint64) error {
	// 组装用户角色关系
	userRoles := make([]model.SysUserRole, 0, len(userIds))
	for _, userId := range userIds {
		userRoles = append(userRoles, model.SysUserRole{
			RoleId: roleId,
			UserId: userId,
		})
	}

	// 批量插入用户角色关系
	return s.db.Create(&userRoles).Error
}

// DeleteRoleUser 删除用户角色关系
func (s *roleServiceImpl) DeleteRoleUser(ctx context.Context, roleId uint64, userId uint64) error {
	return s.db.Where("role_id = ? AND user_id = ?", roleId, userId).Delete(&model.SysUserRole{}).Error
}

// DeleteRoleUsers 批量删除用户角色关系
func (s *roleServiceImpl) DeleteRoleUsers(ctx context.Context, roleId uint64, userIds []uint64) error {
	return s.db.Where("role_id = ? AND user_id IN ?", roleId, userIds).Delete(&model.SysUserRole{}).Error
}

// CheckRoleNameUnique 校验角色名称是否唯一
func (s *roleServiceImpl) CheckRoleNameUnique(ctx context.Context, roleId uint64, roleName string) (bool, error) {
	var role model.SysRole
	err := s.db.Where("role_name = ? AND del_flag = '0'", roleName).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 如果是修改，需要排除自身
	if roleId > 0 && role.RoleId == roleId {
		return true, nil
	}

	return false, nil
}

// CheckRoleKeyUnique 校验角色权限是否唯一
func (s *roleServiceImpl) CheckRoleKeyUnique(ctx context.Context, roleId uint64, roleKey string) (bool, error) {
	var role model.SysRole
	err := s.db.Where("role_key = ? AND del_flag = '0'", roleKey).First(&role).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return true, nil
		}
		return false, err
	}

	// 如果是修改，需要排除自身
	if roleId > 0 && role.RoleId == roleId {
		return true, nil
	}

	return false, nil
}

// CheckRoleAllowed 校验角色是否允许操作
func (s *roleServiceImpl) CheckRoleAllowed(roleId uint64) error {
	if roleId == 1 {
		return errors.New("不允许操作超级管理员角色")
	}
	return nil
}

// 从上下文中获取用户信息
func getUserFromContext(ctx context.Context) *model.SysUser {
	// 在实际实现中，需要从上下文中获取用户信息
	// 这里简化处理，返回nil
	return nil
}
