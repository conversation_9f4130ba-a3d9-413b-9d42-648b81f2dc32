package cache

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/ruoyi-go/config"
	"github.com/ruoyi-go/pkg/logger"
)

var (
	redisClient *redis.Client
	ctx         = context.Background()
)

// Init 初始化Redis连接
func Init() error {
	redisConfig := config.AppConfig.Redis
	redisClient = redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
		Password: redisConfig.Password,
		DB:       redisConfig.Database,
	})

	// 测试连接
	pong, err := redisClient.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("连接Redis失败: %w", err)
	}

	logger.Info("Redis连接成功: " + pong)
	return nil
}

// Set 设置缓存
func Set(key string, value interface{}, expiration time.Duration) error {
	return redisClient.Set(ctx, key, value, expiration).Err()
}

// Get 获取缓存
func Get(key string) (string, error) {
	return redisClient.Get(ctx, key).Result()
}

// GetObject 获取缓存对象
func GetObject(key string, dest interface{}) error {
	return redisClient.Get(ctx, key).Scan(dest)
}

// Delete 删除缓存
func Delete(keys ...string) error {
	return redisClient.Del(ctx, keys...).Err()
}

// Exists 检查键是否存在
func Exists(key string) (bool, error) {
	result, err := redisClient.Exists(ctx, key).Result()
	return result > 0, err
}

// SetNX 设置缓存，如果key不存在
func SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	return redisClient.SetNX(ctx, key, value, expiration).Result()
}

// Expire 设置过期时间
func Expire(key string, expiration time.Duration) error {
	return redisClient.Expire(ctx, key, expiration).Err()
}

// Keys 获取所有匹配的键
func Keys(pattern string) ([]string, error) {
	return redisClient.Keys(ctx, pattern).Result()
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return redisClient
}
