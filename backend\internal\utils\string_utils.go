package utils

import (
	"strconv"
	"strings"
)

// Uint64ToString 将uint64转换为字符串
func Uint64ToString(i uint64) string {
	return strconv.FormatUint(i, 10)
}

// StringsJoin 连接字符串数组
func StringsJoin(elems []string, sep string) string {
	return strings.Join(elems, sep)
}

// ReplaceAncestors 替换祖级列表中的旧路径为新路径
func ReplaceAncestors(ancestors, oldAncestors, newAncestors string) string {
	if oldAncestors == "" || newAncestors == "" {
		return ancestors
	}
	return strings.Replace(ancestors, oldAncestors, newAncestors, 1)
}

// IsEmpty 判断字符串是否为空
func IsEmpty(str string) bool {
	return str == ""
}

// IsNotEmpty 判断字符串是否不为空
func IsNotEmpty(str string) bool {
	return !IsEmpty(str)
}

// DefaultIfEmpty 如果字符串为空，则返回默认值
func DefaultIfEmpty(str, defaultStr string) string {
	if IsEmpty(str) {
		return defaultStr
	}
	return str
}

// TrimToEmpty 去除字符串两端的空格，如果为nil则返回空字符串
func TrimToEmpty(str string) string {
	return strings.TrimSpace(str)
}

// Equals 比较两个字符串是否相等
func Equals(str1, str2 string) bool {
	return str1 == str2
}

// EqualsIgnoreCase 比较两个字符串是否相等，忽略大小写
func EqualsIgnoreCase(str1, str2 string) bool {
	return strings.EqualFold(str1, str2)
}

// ContainsIgnoreCase 判断字符串是否包含另一个字符串，忽略大小写
func ContainsIgnoreCase(str, searchStr string) bool {
	return strings.Contains(strings.ToLower(str), strings.ToLower(searchStr))
}

// StartsWithIgnoreCase 判断字符串是否以另一个字符串开头，忽略大小写
func StartsWithIgnoreCase(str, prefix string) bool {
	return strings.HasPrefix(strings.ToLower(str), strings.ToLower(prefix))
}

// EndsWithIgnoreCase 判断字符串是否以另一个字符串结尾，忽略大小写
func EndsWithIgnoreCase(str, suffix string) bool {
	return strings.HasSuffix(strings.ToLower(str), strings.ToLower(suffix))
}

// ToLowerCaseFirstOne 首字母小写
func ToLowerCaseFirstOne(str string) string {
	if IsEmpty(str) {
		return str
	}
	if len(str) == 1 {
		return strings.ToLower(str)
	}
	return strings.ToLower(str[:1]) + str[1:]
}

// ToUpperCaseFirstOne 首字母大写
func ToUpperCaseFirstOne(str string) string {
	if IsEmpty(str) {
		return str
	}
	if len(str) == 1 {
		return strings.ToUpper(str)
	}
	return strings.ToUpper(str[:1]) + str[1:]
}
