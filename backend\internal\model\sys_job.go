package model

import (
	"time"
)

// SysJob 定时任务调度表 sys_job
type SysJob struct {
	BaseEntity            // 修改为 BaseEntity 以保持与 Java 版本一致
	JobId          uint64 `json:"jobId" gorm:"column:job_id;primaryKey;comment:任务ID"`
	JobName        string `json:"jobName" gorm:"column:job_name;type:varchar(64);not null;comment:任务名称"`
	JobGroup       string `json:"jobGroup" gorm:"column:job_group;type:varchar(64);not null;comment:任务组名"`
	InvokeTarget   string `json:"invokeTarget" gorm:"column:invoke_target;type:varchar(500);not null;comment:调用目标字符串"`
	CronExpression string `json:"cronExpression" gorm:"column:cron_expression;type:varchar(255);comment:cron执行表达式"`
	MisfirePolicy  string `json:"misfirePolicy" gorm:"column:misfire_policy;type:varchar(20);default:3;comment:计划执行错误策略（1立即执行 2执行一次 3放弃执行）"`
	Concurrent     string `json:"concurrent" gorm:"column:concurrent;type:char(1);comment:是否并发执行（0允许 1禁止）"`
	Status         string `json:"status" gorm:"column:status;type:char(1);default:0;comment:状态（0正常 1暂停）"`

	// 非数据库字段
	NextValidTime time.Time `json:"nextValidTime" gorm:"-"` // 下次执行时间
}

// TableName 设置表名
func (SysJob) TableName() string {
	return "sys_job"
}
