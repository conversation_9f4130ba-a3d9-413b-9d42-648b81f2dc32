package utils

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// 缓存键常量
const (
	SYS_CONFIG_KEY    = "sys_config:"     // 参数管理 cache key
	SYS_DICT_KEY      = "sys_dict:"       // 字典管理 cache key
	SYS_DEPT_KEY      = "sys_dept:"       // 部门管理 cache key
	SYS_USER_KEY      = "sys_user:"       // 用户信息 cache key
	SYS_ROLE_KEY      = "sys_role:"       // 角色信息 cache key
	SYS_MENU_KEY      = "sys_menu:"       // 菜单信息 cache key
	CAPTCHA_CODE_KEY  = "captcha_codes:"  // 验证码 cache key
	LOGIN_TOKEN_KEY   = "login_tokens:"   // 登录信息 cache key
	ONLINE_USER_KEY   = "online_user:"    // 在线用户 cache key
	LOGIN_USER_KEY    = "login_user_key:" // 登录用户 cache key
	REPEAT_SUBMIT_KEY = "repeat_submit:"  // 防重提交 cache key
	RATE_LIMIT_KEY    = "rate_limit:"     // 限流 cache key
	JOB_LOCK_KEY      = "job_lock:"       // 定时任务锁 cache key
)

// Cache 是缓存接口
type Cache interface {
	// Set 设置缓存
	Set(key string, value interface{}, expire time.Duration) error
	// Get 获取缓存
	Get(key string) (interface{}, bool)
	// Delete 删除缓存
	Delete(key string) error
	// Clear 清空缓存
	Clear() error
	// AddSet 添加到集合
	AddSet(key string, value interface{}, expire time.Duration) error
	// GetSet 获取集合
	GetSet(key string) ([]interface{}, bool)
	// RemoveSet 从集合中移除
	RemoveSet(key string, value interface{}) error
	// HasSet 判断集合是否包含元素
	HasSet(key string, value interface{}) (bool, error)
	// SetLock 获取锁
	SetLock(key string, expire time.Duration) (bool, error)
	// ReleaseLock 释放锁
	ReleaseLock(key string) error
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	data       map[string]*cacheItem
	setData    map[string]map[string]interface{}
	expiration map[string]time.Time
	mutex      sync.RWMutex
	setMutex   sync.RWMutex
	lockMutex  sync.RWMutex
}

// cacheItem 缓存项
type cacheItem struct {
	value      interface{}
	expiration time.Time
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		data:       make(map[string]*cacheItem),
		setData:    make(map[string]map[string]interface{}),
		expiration: make(map[string]time.Time),
	}

	// 启动过期清理协程
	go cache.cleanExpired()

	return cache
}

// Set 设置缓存
func (c *MemoryCache) Set(key string, value interface{}, expire time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 设置过期时间
	var expiration time.Time
	if expire > 0 {
		expiration = time.Now().Add(expire)
	}

	// 存储缓存项
	c.data[key] = &cacheItem{
		value:      value,
		expiration: expiration,
	}

	return nil
}

// Get 获取缓存
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 获取缓存项
	item, found := c.data[key]
	if !found {
		return nil, false
	}

	// 检查是否过期
	if !item.expiration.IsZero() && item.expiration.Before(time.Now()) {
		return nil, false
	}

	return item.value, true
}

// Delete 删除缓存
func (c *MemoryCache) Delete(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.data, key)
	return nil
}

// Clear 清空缓存
func (c *MemoryCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.data = make(map[string]*cacheItem)
	return nil
}

// AddSet 添加到集合
func (c *MemoryCache) AddSet(key string, value interface{}, expire time.Duration) error {
	c.setMutex.Lock()
	defer c.setMutex.Unlock()

	// 设置过期时间
	if expire > 0 {
		c.expiration[key] = time.Now().Add(expire)
	}

	// 序列化值
	valueStr, err := serializeValue(value)
	if err != nil {
		return err
	}

	// 初始化集合
	if c.setData[key] == nil {
		c.setData[key] = make(map[string]interface{})
	}

	// 添加到集合
	c.setData[key][valueStr] = value

	return nil
}

// GetSet 获取集合
func (c *MemoryCache) GetSet(key string) ([]interface{}, bool) {
	c.setMutex.RLock()
	defer c.setMutex.RUnlock()

	// 获取集合
	set, found := c.setData[key]
	if !found {
		return nil, false
	}

	// 检查是否过期
	expiration, exists := c.expiration[key]
	if exists && !expiration.IsZero() && expiration.Before(time.Now()) {
		return nil, false
	}

	// 转换为切片
	result := make([]interface{}, 0, len(set))
	for _, value := range set {
		result = append(result, value)
	}

	return result, true
}

// RemoveSet 从集合中移除
func (c *MemoryCache) RemoveSet(key string, value interface{}) error {
	c.setMutex.Lock()
	defer c.setMutex.Unlock()

	// 检查集合是否存在
	set, found := c.setData[key]
	if !found {
		return nil
	}

	// 序列化值
	valueStr, err := serializeValue(value)
	if err != nil {
		return err
	}

	// 从集合中移除
	delete(set, valueStr)

	return nil
}

// HasSet 判断集合是否包含元素
func (c *MemoryCache) HasSet(key string, value interface{}) (bool, error) {
	c.setMutex.RLock()
	defer c.setMutex.RUnlock()

	// 检查集合是否存在
	set, found := c.setData[key]
	if !found {
		return false, nil
	}

	// 检查是否过期
	expiration, exists := c.expiration[key]
	if exists && !expiration.IsZero() && expiration.Before(time.Now()) {
		return false, nil
	}

	// 序列化值
	valueStr, err := serializeValue(value)
	if err != nil {
		return false, err
	}

	// 检查元素是否存在
	_, exists = set[valueStr]

	return exists, nil
}

// SetLock 获取锁
func (c *MemoryCache) SetLock(key string, expire time.Duration) (bool, error) {
	c.lockMutex.Lock()
	defer c.lockMutex.Unlock()

	// 检查锁是否存在
	item, found := c.data[key]
	if found {
		// 检查是否过期
		if !item.expiration.IsZero() && item.expiration.Before(time.Now()) {
			// 锁已过期，可以获取
		} else {
			// 锁未过期，无法获取
			return false, nil
		}
	}

	// 获取锁
	c.data[key] = &cacheItem{
		value:      true,
		expiration: time.Now().Add(expire),
	}

	return true, nil
}

// ReleaseLock 释放锁
func (c *MemoryCache) ReleaseLock(key string) error {
	c.lockMutex.Lock()
	defer c.lockMutex.Unlock()

	delete(c.data, key)

	return nil
}

// cleanExpired 清理过期缓存项
func (c *MemoryCache) cleanExpired() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		c.mutex.Lock()
		for key, item := range c.data {
			if !item.expiration.IsZero() && item.expiration.Before(time.Now()) {
				delete(c.data, key)
			}
		}
		c.mutex.Unlock()

		c.setMutex.Lock()
		for key, expiration := range c.expiration {
			if !expiration.IsZero() && expiration.Before(time.Now()) {
				delete(c.setData, key)
				delete(c.expiration, key)
			}
		}
		c.setMutex.Unlock()
	}
}

// serializeValue 序列化值为字符串
func serializeValue(value interface{}) (string, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
		return fmt.Sprintf("%v", v), nil
	default:
		bytes, err := json.Marshal(v)
		if err != nil {
			return "", err
		}
		return string(bytes), nil
	}
}

// 全局缓存实例
var globalCache Cache = NewMemoryCache()

// GetCache 获取缓存实例
func GetCache() Cache {
	return globalCache
}

// SetCache 设置缓存实例
func SetCache(cache Cache) {
	globalCache = cache
}

// CacheUtils 缓存工具类
type CacheUtils struct{}

// NewCacheUtils 创建缓存工具类
func NewCacheUtils() *CacheUtils {
	return &CacheUtils{}
}

// Set 设置缓存
func (c *CacheUtils) Set(key string, value interface{}, expire time.Duration) error {
	return GetCache().Set(key, value, expire)
}

// Get 获取缓存
func (c *CacheUtils) Get(key string) (interface{}, bool) {
	return GetCache().Get(key)
}

// GetString 获取字符串缓存
func (c *CacheUtils) GetString(key string) (string, bool) {
	value, found := GetCache().Get(key)
	if !found {
		return "", false
	}

	str, ok := value.(string)
	if !ok {
		return fmt.Sprintf("%v", value), true
	}

	return str, true
}

// GetInt 获取整数缓存
func (c *CacheUtils) GetInt(key string) (int, bool) {
	value, found := GetCache().Get(key)
	if !found {
		return 0, false
	}

	switch v := value.(type) {
	case int:
		return v, true
	case int64:
		return int(v), true
	case float64:
		return int(v), true
	case string:
		var i int
		_, err := fmt.Sscanf(v, "%d", &i)
		if err != nil {
			return 0, false
		}
		return i, true
	default:
		return 0, false
	}
}

// GetBool 获取布尔缓存
func (c *CacheUtils) GetBool(key string) (bool, bool) {
	value, found := GetCache().Get(key)
	if !found {
		return false, false
	}

	switch v := value.(type) {
	case bool:
		return v, true
	case string:
		if v == "true" {
			return true, true
		} else if v == "false" {
			return false, true
		}
		return false, false
	default:
		return false, false
	}
}

// Delete 删除缓存
func (c *CacheUtils) Delete(key string) error {
	return GetCache().Delete(key)
}

// DeletePattern 根据前缀删除缓存
func (c *CacheUtils) DeletePattern(pattern string) error {
	// 内存缓存不支持模式删除，需要遍历所有key
	// 这里简化处理，仅支持前缀匹配
	cache, ok := GetCache().(*MemoryCache)
	if !ok {
		return fmt.Errorf("unsupported cache type")
	}

	cache.mutex.Lock()
	defer cache.mutex.Unlock()

	for key := range cache.data {
		if len(key) >= len(pattern) && key[:len(pattern)] == pattern {
			delete(cache.data, key)
		}
	}

	return nil
}

// AddSet 添加到集合
func (c *CacheUtils) AddSet(key string, value interface{}, expire time.Duration) error {
	return GetCache().AddSet(key, value, expire)
}

// GetSet 获取集合
func (c *CacheUtils) GetSet(key string) ([]interface{}, bool) {
	return GetCache().GetSet(key)
}

// RemoveSet 从集合中移除
func (c *CacheUtils) RemoveSet(key string, value interface{}) error {
	return GetCache().RemoveSet(key, value)
}

// HasSet 判断集合是否包含元素
func (c *CacheUtils) HasSet(key string, value interface{}) (bool, error) {
	return GetCache().HasSet(key, value)
}

// SetLock 获取锁
func (c *CacheUtils) SetLock(key string, expire time.Duration) (bool, error) {
	return GetCache().SetLock(key, expire)
}

// ReleaseLock 释放锁
func (c *CacheUtils) ReleaseLock(key string) error {
	return GetCache().ReleaseLock(key)
}
