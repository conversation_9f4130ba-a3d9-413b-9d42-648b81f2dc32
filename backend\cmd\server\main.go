package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/ruoyi-go/internal/api/controller"
	"github.com/ruoyi-go/internal/api/router"
	"github.com/ruoyi-go/internal/service"
	"github.com/ruoyi-go/pkg/logger"
)

// @title RuoYi-Go API
// @version 1.0
// @description RuoYi管理系统后端API - 基于Gin+GORM
// @BasePath /api
func main() {
	// 初始化配置
	initConfig()

	// 初始化日志
	logger.Setup()

	// 初始化数据库
	db := initDatabase()

	// 初始化服务
	userService := service.NewUserService(db)

	// 初始化控制器
	userController := controller.NewUserController(userService)

	// 设置Gin模式
	gin.SetMode(viper.GetString("server.mode"))

	// 创建Gin实例
	r := gin.New()

	// 使用日志和恢复中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 注册路由
	apiGroup := r.Group(viper.GetString("server.context_path"))
	router.RegisterUserRoutes(apiGroup, userController)

	// 启动服务器
	addr := fmt.Sprintf("%s:%d", viper.GetString("server.host"), viper.GetInt("server.port"))
	srv := &http.Server{
		Addr:    addr,
		Handler: r,
	}

	fmt.Printf("Starting server at %s\n", addr)
	if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("listen: %s\n", err)
	}
}

// 初始化配置
func initConfig() {
	viper.SetConfigName("application")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Failed to read config: %v", err)
	}
}

// 初始化数据库
func initDatabase() *gorm.DB {
	// 构建DSN
	dsn := fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s",
		viper.GetString("database.username"),
		viper.GetString("database.password"),
		viper.GetString("database.host"),
		viper.GetInt("database.port"),
		viper.GetString("database.database"),
	)

	// 日志级别
	var logLevel logger.LogLevel
	if viper.GetBool("database.show_sql") {
		logLevel = logger.Info
	} else {
		logLevel = logger.Silent
	}

	// 连接数据库
	db, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		log.Fatalf("Failed to connect database: %v", err)
	}

	// 设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get database connection: %v", err)
	}

	sqlDB.SetMaxIdleConns(viper.GetInt("database.max_idle_conns"))
	sqlDB.SetMaxOpenConns(viper.GetInt("database.max_open_conns"))
	sqlDB.SetConnMaxLifetime(viper.GetDuration("database.conn_max_lifetime") * 60)

	return db
}
