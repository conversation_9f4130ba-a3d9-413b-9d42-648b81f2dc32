package model

// SysPost 岗位表 sys_post
type SysPost struct {
	BaseEntity        // 修改为 BaseEntity 以保持与 Java 版本一致
	PostId     uint64 `json:"postId" gorm:"column:post_id;primaryKey;comment:岗位ID"`
	PostCode   string `json:"postCode" gorm:"column:post_code;type:varchar(64);not null;comment:岗位编码"`
	PostName   string `json:"postName" gorm:"column:post_name;type:varchar(50);not null;comment:岗位名称"`
	PostSort   int    `json:"postSort" gorm:"column:post_sort;not null;comment:显示顺序"`
	Status     string `json:"status" gorm:"column:status;type:char(1);not null;comment:状态（0正常 1停用）"`

	// 非数据库字段
	Flag bool `json:"flag" gorm:"-"` // 用户是否存在此岗位标识
}

// TableName 设置表名
func (SysPost) TableName() string {
	return "sys_post"
}
