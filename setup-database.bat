@echo off
echo ==================================
echo       若依系统数据库配置脚本
echo ==================================

set /p MYSQL_USER=请输入MySQL用户名(默认root): 
if "%MYSQL_USER%"=="" set MYSQL_USER=root

set /p MYSQL_PASS=请输入MySQL密码: 

echo 1. 创建数据库...
echo CREATE DATABASE IF NOT EXISTS `ry-vue` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci; > create_db.sql

echo 2. 使用MySQL命令导入数据库...
mysql -u%MYSQL_USER% -p%MYSQL_PASS% < create_db.sql
if %ERRORLEVEL% NEQ 0 (
    echo 创建数据库失败，请检查MySQL用户名和密码是否正确
    goto :end
)

echo 3. 导入数据表结构和基础数据...
mysql -u%MYSQL_USER% -p%MYSQL_PASS% ry-vue < ruoyi-java\sql\ry_20250522.sql
if %ERRORLEVEL% NEQ 0 (
    echo 导入数据表失败
    goto :end
)

echo 4. 导入定时任务相关表...
mysql -u%MYSQL_USER% -p%MYSQL_PASS% ry-vue < ruoyi-java\sql\quartz.sql
if %ERRORLEVEL% NEQ 0 (
    echo 导入定时任务表失败
    goto :end
)

echo 5. 更新数据库配置...
echo 修改数据库连接信息...

powershell -Command "(Get-Content ruoyi-java\ruoyi-admin\src\main\resources\application-druid.yml) -replace 'password: password', 'password: %MYSQL_PASS%' | Set-Content ruoyi-java\ruoyi-admin\src\main\resources\application-druid.yml"

echo 数据库配置完成!

:end
del create_db.sql
pause 