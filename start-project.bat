@echo off
echo ==================================
echo       若依系统启动脚本
echo ==================================

echo 检查Redis是否已运行...
tasklist /fi "imagename eq redis-server.exe" | find "redis-server.exe" > nul
if not errorlevel 1 (
    echo Redis服务已经在运行中...
) else (
    echo 1. 启动Redis服务...
    start "" "redis\redis-server.exe" "redis\redis.windows.conf"
    echo Redis服务已启动
)

echo 2. 等待Redis完全启动...
timeout /t 3 /nobreak > nul

echo 检查后端服务是否已运行...
tasklist /fi "imagename eq java.exe" | find "java.exe" > nul
if not errorlevel 1 (
    echo 后端服务已经在运行中...
) else (
    echo 3. 启动后端服务...
    cd ruoyi-java\ruoyi-admin\target
    start "RuoYi后端" java -jar ruoyi-admin.jar
    cd ..\..\..
    echo 后端服务正在启动中，请稍候...
)

echo 4. 等待后端服务完全启动...
timeout /t 10 /nobreak > nul

echo 5. 启动前端服务...
start "RuoYi前端" cmd /c "npm run dev"

echo ==================================
echo 所有服务已启动！
echo 前端访问地址: http://localhost:80
echo 默认用户名: admin
echo 默认密码: admin123
echo ==================================

echo 按任意键退出此窗口，服务将继续在后台运行...
pause 