# RuoYi-Go 核心结构说明

## 数据权限过滤机制

RuoYi框架提供了灵活的数据权限过滤机制，能够根据用户角色和部门关系动态构建SQL查询条件，限制用户只能查看特定范围内的数据。

### 数据权限类型

系统支持以下几种数据权限类型：

1. **全部数据权限（1）**：可以查看所有数据。
2. **自定数据权限（2）**：可以查看自定义指定部门的数据。
3. **本部门数据权限（3）**：只能查看本部门的数据。
4. **本部门及以下数据权限（4）**：可以查看本部门及以下部门的数据。
5. **仅本人数据权限（5）**：只能查看本人创建的数据。

### 实现方式

数据权限过滤的实现主要涉及以下几个组件：

#### 1. 数据权限常量定义

位于 `middleware/data_scope.go` 文件中，定义了与数据权限相关的常量：

```go
const (
    // 数据权限类型
    DATA_SCOPE_ALL        = "1" // 全部数据权限
    DATA_SCOPE_CUSTOM     = "2" // 自定数据权限
    DATA_SCOPE_DEPT       = "3" // 本部门数据权限
    DATA_SCOPE_DEPT_AND_CHILD = "4" // 本部门及以下数据权限
    DATA_SCOPE_SELF       = "5" // 仅本人数据权限
    
    // 数据权限参数键名
    DATA_SCOPE = "dataScope" // 数据范围参数名
)
```

#### 2. 数据权限注解

位于 `annotation/data_scope.go` 文件中，定义了与数据权限相关的注解：

```go
// DataPermission 数据权限过滤注解
type DataPermission struct {
    // 部门别名
    DeptAlias string
    // 用户别名
    UserAlias string
    // 权限字符（用于多个角色匹配符合要求的权限）
    Permission string
    // 是否过滤数据权限，默认过滤
    IsFilter bool
}

// MatchingPermission 匹配权限字符
func MatchingPermission(permissions []string, permission string) bool {
    // 如果配置了权限字符串，则进行匹配
    if permission != "" {
        for _, p := range permissions {
            if p == permission {
                return true
            }
        }
        return false
    }
    return true
}
```

#### 3. 数据权限过滤方法

位于 `middleware/data_scope.go` 文件中的 `DataScopeFilter` 方法是核心实现：

```go
// DataScopeFilter 数据权限过滤
func DataScopeFilter(ctx context.Context, user *model.SysUser, deptAlias, userAlias, permission string, baseEntity *model.BaseEntity) {
    // ...
    // 根据角色权限范围构建SQL条件
    // ...
}
```

该方法会根据用户的角色和部门关系，构建相应的SQL条件，并将其存储在 `baseEntity.Params` 中，供后续查询使用。

#### 4. 在服务层中应用数据权限过滤

在各个服务层方法中，通过调用 `DataScopeFilter` 方法来应用数据权限过滤：

```go
// 以角色服务的ListRoles方法为例
func (s *roleServiceImpl) ListRoles(ctx context.Context, role *model.SysRole, pageNum, pageSize int) ([]*model.SysRole, int64, error) {
    // ...
    baseEntity := &model.BaseEntity{}
    // ...
    
    // 获取当前登录用户
    user := getUserFromContext(ctx)
    if user != nil {
        // 调用数据权限过滤
        middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)
        
        // 应用数据权限过滤
        if baseEntity.Params != nil {
            if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
                // 拼接数据权限SQL
                query = query.Joins("LEFT JOIN sys_role_dept rd ON rd.role_id = sys_role.role_id")
                query = query.Joins("LEFT JOIN sys_dept d ON d.dept_id = rd.dept_id")
                query = query.Where(dataScope)
                // 去重
                query = query.Group("sys_role.role_id")
            }
        }
    }
    // ...
}
```

### 数据权限相关表

数据权限过滤机制依赖于以下几个关联表：

1. **sys_role**：角色表，包含 `data_scope` 字段，用于定义角色的数据权限范围。
2. **sys_role_dept**：角色部门关联表，用于自定义数据权限场景下的部门范围定义。
3. **sys_user_role**：用户角色关联表，用于获取用户所属角色。
4. **sys_dept**：部门表，用于构建部门层级关系。

### 数据权限使用示例

在业务层中使用数据权限过滤的典型流程如下：

1. 在查询方法中创建基础实体：`baseEntity := &model.BaseEntity{}`
2. 从上下文中获取当前登录用户：`user := getUserFromContext(ctx)`
3. 调用数据权限过滤方法：`middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)`
4. 将生成的SQL条件应用到查询中
5. 使用关联查询将相关表连接起来
6. 执行查询并返回结果

通过这种方式，系统能够根据用户的角色和部门关系，自动限制其只能查看有权限的数据，实现了灵活而安全的数据访问控制。

## 部门与菜单服务实现

除了核心的数据权限过滤机制外，我们还实现了部门和菜单的服务层，这两者是系统权限管理的重要组成部分。

### 部门服务 (DeptService)

部门服务主要提供部门的CRUD操作以及树形结构构建功能：

1. **部门查询**：支持根据ID查询、条件查询列表，并应用数据权限过滤
2. **部门操作**：新增、修改、删除部门，涉及祖级列表的维护
3. **树形结构**：构建部门树和部门下拉选择树
4. **业务校验**：检查部门名称唯一性、检查是否存在用户、检查是否有子部门等

关键实现：

```go
// 部门列表查询，应用数据权限过滤
func (s *deptServiceImpl) ListDepts(ctx context.Context, dept *model.SysDept) ([]*model.SysDept, error) {
    // ...
    // 应用数据权限过滤
    baseEntity := &model.BaseEntity{}
    // ...
    user := getUserFromContext(ctx)
    if user != nil {
        middleware.DataScopeFilter(ctx, user, "d", "", "", baseEntity)
        if baseEntity.Params != nil {
            if dataScope, ok := baseEntity.Params[middleware.DATA_SCOPE].(string); ok && dataScope != "" {
                query = query.Where(dataScope)
            }
        }
    }
    // ...
}

// 构建部门树
func (s *deptServiceImpl) BuildDeptTree(depts []*model.SysDept) []*model.SysDept {
    // 创建映射，便于构建树形结构
    deptMap := make(map[uint64]*model.SysDept)
    for _, dept := range depts {
        deptMap[dept.DeptId] = dept
    }
    
    // 构建树形结构
    var trees []*model.SysDept
    for _, dept := range depts {
        if dept.ParentId == 0 {
            trees = append(trees, dept)
            continue
        }
        
        if parent, ok := deptMap[dept.ParentId]; ok {
            if parent.Children == nil {
                parent.Children = make([]*model.SysDept, 0)
            }
            parent.Children = append(parent.Children, dept)
        } else {
            trees = append(trees, dept)
        }
    }
    
    return trees
}
```

### 菜单服务 (MenuService)

菜单服务主要提供菜单管理和前端路由构建功能：

1. **菜单查询**：支持根据ID查询、条件查询列表
2. **菜单操作**：新增、修改、删除菜单
3. **角色菜单**：根据角色ID查询菜单、权限标识
4. **用户菜单**：根据用户ID查询菜单、菜单树
5. **前端路由**：构建前端路由数据，支持多级嵌套

关键实现：

```go
// 根据用户ID查询菜单
func (s *menuServiceImpl) GetMenusByUserId(ctx context.Context, userId uint64) ([]*model.SysMenu, error) {
    // 超级管理员拥有所有菜单
    if userId == 1 {
        // ...
    }
    
    // 普通用户查询关联的菜单
    err := s.db.Model(&model.SysMenu{}).
        Distinct("sys_menu.*").
        Joins("LEFT JOIN sys_role_menu rm ON rm.menu_id = sys_menu.menu_id").
        Joins("LEFT JOIN sys_user_role ur ON ur.role_id = rm.role_id").
        Where("ur.user_id = ? AND sys_menu.status = ? AND sys_menu.del_flag = '0'", userId, SHOW).
        Order("sys_menu.parent_id, sys_menu.order_num").
        Find(&menus).Error
    
    return menus, err
}

// 构建前端路由
func (s *menuServiceImpl) BuildRouters(menus []*model.SysMenu) []*model.RouterVo {
    var routers []*model.RouterVo
    for _, menu := range menus {
        // 只处理目录和菜单
        if menu.ParentId == 0 && (menu.MenuType == TYPE_DIR || menu.MenuType == TYPE_MENU) {
            router := s.getRouter(menu)
            
            // 处理子路由
            if menu.Children != nil && len(menu.Children) > 0 {
                router.AlwaysShow = true
                router.Redirect = "noRedirect"
                router.Children = s.buildChildrenRouters(menu.Children)
            }
            
            routers = append(routers, router)
        }
    }
    
    return routers
}
```

### 树形结构处理

部门和菜单都涉及树形结构的处理，我们实现了通用的树形选择器模型：

```go
// TreeSelect 树形选择器结构
type TreeSelect struct {
    Id       uint64       `json:"id"`       // 节点ID
    Label    string       `json:"label"`    // 节点名称
    Children []*TreeSelect `json:"children"` // 子节点
}
```

通过递归方式构建树形结构：

```go
// 构建树形结构
func buildTree(items []interface{}, getId func(item interface{}) uint64, getParentId func(item interface{}) uint64, transform func(item interface{}) interface{}) []interface{} {
    // 创建映射，便于构建树形结构
    itemMap := make(map[uint64]interface{})
    for _, item := range items {
        itemMap[getId(item)] = item
    }
    
    // 构建树形结构
    var trees []interface{}
    for _, item := range items {
        parentId := getParentId(item)
        
        if parentId == 0 {
            trees = append(trees, transform(item))
            continue
        }
        
        if parent, ok := itemMap[parentId]; ok {
            // 将当前节点加入父节点的子节点列表
            // ...
        } else {
            trees = append(trees, transform(item))
        }
    }
    
    return trees
}
```

通过这些服务实现，系统能够实现完整的权限管理功能，包括部门管理、菜单管理、角色授权等，并且能够根据用户的权限自动过滤数据，确保数据访问安全。 