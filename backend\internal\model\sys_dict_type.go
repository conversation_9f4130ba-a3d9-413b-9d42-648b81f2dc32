package model

// SysDictType 字典类型表 sys_dict_type
type SysDictType struct {
	BaseModel
	DictId   uint64 `json:"dictId" gorm:"column:dict_id;primaryKey;comment:字典主键"`
	DictName string `json:"dictName" gorm:"column:dict_name;type:varchar(100);comment:字典名称"`
	DictType string `json:"dictType" gorm:"column:dict_type;type:varchar(100);comment:字典类型"`
	Status   string `json:"status" gorm:"column:status;type:char(1);default:0;comment:状态（0正常 1停用）"`
}

// TableName 设置表名
func (SysDictType) TableName() string {
	return "sys_dict_type"
}
